import fs from 'fs/promises';
import os from 'os';
import path from 'path';
import { z } from 'zod';

import { fileExists } from '@shared/ide/utils/FileUtils';
import { logger } from '@shared/webUtils/log';

import { IDELocationResolver, VSCodeIDEInfos } from './IDEInfo';
import { McpServerName } from './McpTypes';
import { NodeInstaller } from './NodeInstaller';

const log = logger('VSCodeMcpInstaller');

const SettingsSchema = z
    .object({
        mcp: z
            .object({
                servers: z.record(z.string(), z.object({}).passthrough()).optional(),
            })
            .passthrough()
            .optional(),
    })
    .passthrough();

type SettingsType = z.infer<typeof SettingsSchema>;

export class VSCodeMcpInstaller {
    static async install(mcpScript: string) {
        // If cursor is not installed, we're done
        const vscodeInstallations = await IDELocationResolver.instance().findLocations([
            VSCodeIDEInfos.vscode,
            VSCodeIDEInfos.vscodeInsiders,
        ]);

        if (!fileExists(this.getSettingsJsonFolder()) && vscodeInstallations.length === 0) {
            return;
        }

        // Ensure node is installed
        const nodeLocation = await NodeInstaller.instance().ensureInstalled();

        await fs.mkdir(this.getSettingsJsonFolder(), { recursive: true });

        const settingsObject = await this.getSettingsJson();
        settingsObject.mcp = settingsObject?.mcp ?? {};
        settingsObject.mcp.servers = settingsObject.mcp.servers ?? {};
        settingsObject.mcp.servers[McpServerName] = {
            type: 'stdio',
            command: nodeLocation.binPath,
            args: [mcpScript],
        };

        await fs.writeFile(this.getSettingsJsonPath(), JSON.stringify(settingsObject, null, 2), 'utf-8');
    }

    private static getSettingsJsonFolder() {
        switch (os.platform()) {
            case 'darwin':
                return path.join(os.homedir(), 'Library', 'Application Support', 'Code', 'User');

            case 'win32':
                return path.join(os.homedir(), 'AppData', 'Code', 'User');

            default:
                return path.join(os.homedir(), '.config', 'Code', 'User');
        }
    }

    private static getSettingsJsonPath() {
        return path.join(this.getSettingsJsonFolder(), 'settings.json');
    }

    private static async getSettingsJson(): Promise<SettingsType> {
        let mcp: string;
        try {
            mcp = await fs.readFile(this.getSettingsJsonPath(), 'utf-8');
        } catch (error) {
            log.warn('Could not read VSCode user settings', error);
            // No user settings file -- start one from scratch
            return {};
        }

        try {
            const jsonContent = JSON.parse(mcp);
            return SettingsSchema.parse(jsonContent);
        } catch (error) {
            // If we can't parse the user settings file, we will let exceptions bubble,
            // because we don't want to accidentally edit the user settings in a way that
            // is destructive -- this file is highly customized and important to users.
            // This means MCP installation will fail.
            log.error('Could not read VSCode user settings', error);
            throw error;
        }
    }
}
