import { ReactNode, useCallback, useEffect, useMemo, useRef } from 'react';

import { useStream } from '@shared/stores/DataCacheStream';
import { FeatureSettingsStoreTraits } from '@shared/stores/FeatureSettingsStoreType';
import { SampleQuestionsStoreTraits } from '@shared/stores/SampleQuestionsStoreTraits';
import { TeamMemberPreferencesStoreTraits } from '@shared/stores/TeamMemberPreferencesStoreTraits';
import { TeamStoreTraits } from '@shared/stores/TeamStoreTypes';
import { useStore } from '@shared/stores/useStore';
import { useStreamEffect } from '@shared/stores/useStreamEffect';
import { useUserSettingState } from '@shared/stores/useUserSettingState';
import { ClientWorkspace } from '@shared/webComponents/ClientWorkspace/ClientWorkspace';
import {
    FocusActionStreamTraits,
    UpsellStreamTraits,
} from '@shared/webComponents/ClientWorkspace/ClientWorkspaceStreamTraits';
import { useFocusManager } from '@shared/webComponents/FocusManager/FocusManager';
import { Icon } from '@shared/webComponents/Icon/Icon';
import { IncognitoAlwaysOnDialog } from '@shared/webComponents/Incognito/IncognitoAlwaysOnDialog';
import { IntegrationsDescription } from '@shared/webComponents/IntegrationsDescription/IntegrationsDescription';
import { Loading } from '@shared/webComponents/Loading/Loading';
import { MessageEditor, MessageEditorForwardedRef } from '@shared/webComponents/MessageEditor';
import { useModalContext } from '@shared/webComponents/Modal/ModalContext';
import { PlanExpiryBannerRenderer } from '@shared/webComponents/PlanBanners/PlanExpiryBanner';
import { SuggestionView } from '@shared/webComponents/SuggestionView/SuggestionView';
import { useAnswersTutorialDialog } from '@shared/webComponents/Tutorial/AnswersTutorial/AnswersTutorialDialog';
import { TutorialTooltipManagerProvider } from '@shared/webComponents/Tutorial/TutorialTooltipManager';
import { UpsellDialogIncognitoMode } from '@shared/webComponents/UpsellDialog/UpsellDialogIncognitoMode';

import unblockedBotIcon from '@clientAssets/unblockedbot.svg';

import { Block } from '../../../common/build/generated/source/proto/main/ts_proto/Message';

import './AskQuestion.scss';

interface Props {
    teamId: string;
}
export const AskQuestion = ({ teamId }: Props) => {
    const [isPrivate, setIsPrivate] = useUserSettingState('incognitoMode');
    const editorRef = useRef<MessageEditorForwardedRef>(null);
    const { openModal } = useModalContext();
    const upsellState = useStream(
        () => ClientWorkspace.instance().getStream(UpsellStreamTraits, { $case: 'upsell', teamId }),
        [teamId]
    );
    const featureSettingsStore = useStore(FeatureSettingsStoreTraits, { teamId });
    const featureSettings = useStream(() => featureSettingsStore.stream, [featureSettingsStore]);

    const isDsacEnabled = useMemo(
        () => featureSettings?.$case === 'ready' && !!featureSettings.settings.enableDataSourceAccessControl,
        [featureSettings]
    );

    const teamStore = useStore(TeamStoreTraits, {});
    const team = useStream(
        () =>
            teamStore.teams.map((teamState) =>
                teamState.$case === 'ready' ? teamState.value.find((team) => team.id === teamId) : undefined
            ),
        [teamStore, teamId]
    );

    const privateable = useMemo(() => {
        if (!team) {
            return false;
        }

        return !!team.capabilities.showIncognito;
    }, [team]);

    const togglePrivate = useCallback(
        (isPrivate: boolean) => {
            if (!privateable) {
                return;
            }

            if (isDsacEnabled) {
                openModal(
                    <IncognitoAlwaysOnDialog
                        teamId={teamId}
                        canConfigure={featureSettings?.$case === 'ready' && featureSettings.isAllowedToUpdate}
                    />
                );
                return;
            }

            if (upsellState?.$case === 'ready' && upsellState.incognitoMode && isPrivate) {
                openModal(<UpsellDialogIncognitoMode teamId={teamId} template={upsellState.incognitoMode} />);
            } else {
                setIsPrivate(isPrivate);
            }
        },
        [openModal, setIsPrivate, teamId, upsellState, privateable, isDsacEnabled, featureSettings]
    );

    const { setDefaultFocusQuery, resetDefaultFocusQuery } = useFocusManager();
    useStreamEffect(
        () =>
            ClientWorkspace.instance()
                .getStream(FocusActionStreamTraits, { $case: 'focusAction' })
                .filter((action) => action.$case === 'askUnblockedInput'),
        [],
        () => editorRef.current?.focus()
    );

    const store = useStore(SampleQuestionsStoreTraits, { teamId });
    const sampleQuestionsState = useStream(() => store.stream, [store], { $case: 'loading' });
    const preferencesStore = useStore(TeamMemberPreferencesStoreTraits, { teamId });
    const preferencesState = useStream(() => preferencesStore.presetStream, [preferencesStore], { $case: 'loading' });

    useEffect(() => {
        setDefaultFocusQuery('.ask_question .message_editor .message_editor__content__editable');
        return () => {
            resetDefaultFocusQuery();
        };
    }, [resetDefaultFocusQuery, setDefaultFocusQuery]);
    useAnswersTutorialDialog(teamId);

    const askQuestion = useCallback(
        async (blocks: Block[], mentions: string[]) => {
            await ClientWorkspace.instance().handleAction({
                $case: 'askQuestion',
                content: { $case: 'blocks', content: blocks, mentions },
                teamId,
                isPrivate,
            });
            store.refresh({});
        },
        [store, teamId, isPrivate]
    );

    const openUrl = useCallback(
        (url: string) =>
            ClientWorkspace.instance().handleAction({
                $case: 'openUrl',
                url,
            }),
        []
    );

    if (sampleQuestionsState.$case === 'loading' || preferencesState.$case === 'loading') {
        return <Loading />;
    }

    let title: string;
    let subtitle: string | undefined;
    let body: ReactNode;

    switch (sampleQuestionsState.$case) {
        case 'unauthenticated':
            title = 'Ask Unblocked';
            body = (
                <IntegrationsDescription teamId={teamId} onOpenDashboardUrl={openUrl} showConfigureIntegrationsButton />
            );
            break;

        // Person has not asked a question yet: show sample questions
        case 'ready':
            title = `Welcome, ${sampleQuestionsState.personName}!`;
            subtitle = 'Ready to ask your first question?';
            body = (
                <SuggestionsContent
                    teamId={teamId}
                    suggestions={sampleQuestionsState.questions.map((question) => question.question)}
                />
            );
            break;

        // Person has asked questions
        case 'none':
            title = `Hi ${sampleQuestionsState.personName}, what can I answer for you?`;
            body = (
                <IntegrationsDescription teamId={teamId} onOpenDashboardUrl={openUrl} showConfigureIntegrationsButton />
            );
            break;
    }

    return (
        <TutorialTooltipManagerProvider tutorialType={isDsacEnabled ? 'dsacAnswers' : 'answers'}>
            <div className="ask_question">
                <div>
                    {team ? (
                        <PlanExpiryBannerRenderer
                            team={team}
                            variant="unblocked"
                            buttonVariant="secondary"
                            showAction
                            floating
                        />
                    ) : null}
                </div>
                <div className="ask_question__content">
                    <Icon
                        icon={
                            preferencesState.value.selected
                                ? preferencesState.value.selected.avatarUrl
                                : unblockedBotIcon
                        }
                        size={120}
                    />
                    <h1>{title}</h1>
                    {subtitle && <h2>{subtitle}</h2>}
                    {body}
                </div>

                <MessageEditor
                    teamId={teamId}
                    autofocus
                    placeholder={
                        privateable && (isPrivate || isDsacEnabled) ? 'Ask a question privately' : 'Ask a question'
                    }
                    onSubmit={askQuestion}
                    ref={editorRef}
                    isPrivate={privateable ? (isDsacEnabled ? true : isPrivate) : undefined}
                    onSetPrivate={privateable ? togglePrivate : undefined}
                />
            </div>
        </TutorialTooltipManagerProvider>
    );
};

const SuggestionsContent = ({ suggestions, teamId }: { suggestions: string[]; teamId: string }) => {
    const askSuggestion = useCallback(
        async (suggestion: string) => {
            await ClientWorkspace.instance().handleAction({
                $case: 'askQuestion',
                content: { $case: 'string', content: suggestion },
                teamId,
                isPrivate: false,
            });
        },
        [teamId]
    );
    if (!suggestions?.length) {
        return null;
    }

    return (
        <div className="ask_question__suggestions_content">
            <h3>
                Pick one of the <span className="ask_question__suggestion_label">suggested questions</span> or ask your
                own:
            </h3>
            <SuggestionView suggestions={suggestions} askSuggestion={askSuggestion} />
        </div>
    );
};
