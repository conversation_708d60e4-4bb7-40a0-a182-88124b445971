import { Group, TeamMember } from '@shared/api/generatedApi';

export enum TeamMembersSort {
    nameAsc = 'nameAsc',
    nameDesc = 'nameDesc',
    questionsAsc = 'questionsAsc',
    questionsDesc = 'questionsDesc',
    createdAtAsc = 'createdAtAsc',
    createdAtDesc = 'createdAtDesc',
    lastActiveAsc = 'lastActiveAsc',
    lastActiveDesc = 'lastActiveDesc',
    invitedAsc = 'invitedAsc',
    invitedDesc = 'invitedDesc',
    // roleAsc = 'roleAsc',
    // roleDesc = 'roleDesc',
    closestSearchMatch = 'closestSearchMatch',
    hasLicenseAsc = 'hasLicenseAsc',
    hasLicenseDesc = 'hasLicenseDesc',
}

export type TeamMemberWithGroup = TeamMember & {
    groupAggregates: Group[];
    inheritedGroupAggregate?: Group;
};
