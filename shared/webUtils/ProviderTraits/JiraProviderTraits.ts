import { IconSrc } from '@shared/webComponents/Icon/Icon';

import { BrandIcons } from '../BrandIcons';
import { StringPluralCounts } from '../StringsHelper/StringsHelper';
import { ProviderTraits } from './ProviderTraits';

export class JiraProviderTraits extends ProviderTraits {
    readonly displayName: string = 'Jira';
    readonly shortenedDisplayName: string = 'Jira';

    threadLabel(includeProvider: boolean = true): string {
        return includeProvider ? 'Jira issue' : 'issue';
    }

    projectLabelCounts(includeProvider: boolean = true): StringPluralCounts {
        return includeProvider
            ? { singular: 'Jira project', plural: 'Jira projects' }
            : { singular: 'project', plural: 'projects' };
    }

    workspaceLabelCounts(includeProvider: boolean = true): StringPluralCounts {
        return includeProvider
            ? { singular: 'Jira site', plural: 'Jira sites' }
            : { singular: 'site', plural: 'sites' };
    }

    iconSrc(color?: boolean): IconSrc {
        return color ? BrandIcons.jira : BrandIcons.jiraDesaturated;
    }
}
