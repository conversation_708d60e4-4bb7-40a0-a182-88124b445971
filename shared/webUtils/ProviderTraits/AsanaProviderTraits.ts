import { IconSrc } from '@shared/webComponents/Icon/Icon';

import { BrandIcons } from '../BrandIcons';
import { StringPluralCounts } from '../StringsHelper/StringsHelper';
import { ProviderTraits } from './ProviderTraits';

export class AsanaProviderTraits extends ProviderTraits {
    readonly displayName: string = 'Asana';
    readonly shortenedDisplayName: string = 'Asana';

    threadLabel(includeProvider: boolean = true): string {
        return includeProvider ? 'Asana task' : 'task';
    }

    projectLabelCounts(includeProvider: boolean = true): StringPluralCounts {
        return includeProvider
            ? { singular: 'Asana project', plural: 'Asana projects' }
            : { singular: 'project', plural: 'projects' };
    }

    workspaceLabelCounts(includeProvider: boolean = true): StringPluralCounts {
        return includeProvider
            ? { singular: 'Asana workspace', plural: 'Asana workspaces:' }
            : { singular: 'workspace', plural: 'workspaces' };
    }

    iconSrc(): IconSrc {
        return BrandIcons.asana;
    }
}
