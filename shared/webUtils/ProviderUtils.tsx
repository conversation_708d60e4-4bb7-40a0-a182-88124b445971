import { ReactNode } from 'react';

import { IconSrc } from '@shared/webComponents/Icon/Icon';

import { Provider } from '../api/models';
import { ProviderTraitsUtil } from './ProviderTraits/ProviderTraitsUtil';
import { StringPluralCounts } from './StringsHelper/StringsHelper';

export const getProviderDisplayName = (provider: Provider, shortened: boolean = true): string => {
    const traits = ProviderTraitsUtil.get(provider);
    return shortened ? traits.shortenedDisplayName : traits.displayName;
};

export const getProviderProjectLabelCounts = (provider: Provider, includeProvider = true): StringPluralCounts => {
    const traits = ProviderTraitsUtil.get(provider);
    return traits.projectLabelCounts(includeProvider);
};

export const getProviderWorkspaceLabelCounts = (provider: Provider, includeProvider = true): StringPluralCounts => {
    const traits = ProviderTraitsUtil.get(provider);
    return traits.workspaceLabelCounts(includeProvider);
};

export type ClientIntegrationType =
    | 'source'
    | 'documentation'
    | 'issueTracker'
    | 'messaging'
    | 'invalid'
    | 'continuousIntegration';

export const getClientIntegrationTypeLabel = (type: ClientIntegrationType) => {
    switch (type) {
        case 'source':
            return 'Source Code';
        case 'documentation':
            return 'Documentation';
        case 'issueTracker':
            return 'Project Management';
        case 'messaging':
            return 'Messaging Platforms';
        case 'continuousIntegration':
            return 'Continuous Integration';
    }
};

export const getProviderClassType = (provider: Provider): ClientIntegrationType => {
    switch (provider) {
        case Provider.AzureDevOps:
        case Provider.Bitbucket:
        case Provider.BitbucketDataCenter:
        case Provider.Github:
        case Provider.GithubEnterprise:
        case Provider.Gitlab:
        case Provider.GitlabSelfHosted:
            return 'source';

        case Provider.Asana:
        case Provider.Jira:
        case Provider.JiraDataCenter:
        case Provider.Linear:
            return 'issueTracker';

        case Provider.Slack:
            return 'messaging';

        case Provider.Confluence:
        case Provider.ConfluenceDataCenter:
        case Provider.StackOverflowTeams:
        case Provider.Notion:
        case Provider.Google:
        case Provider.GoogleDriveWorkspace:
        case Provider.Web:
        case Provider.Coda:
        case Provider.CustomIntegration:
            return 'documentation';

        case Provider.BuildKite:
        case Provider.Circleci:
        case Provider.GithubActions:
            return 'continuousIntegration';

        // These are not expected to be used at all when grouping
        case Provider.Aws:
        case Provider.Unblocked:
        case Provider.AwsIdentityCenter:
        case Provider.GoogleWorkspace:
        case Provider.MicrosoftEntra:
        case Provider.Okta:
        case Provider.PingOne:
        case Provider.Saml:
        case Provider.UnknownDefaultOpenApi:
            return 'invalid';
    }
};

export const getProviderOrgNode = (provider: Provider, short?: boolean): string => {
    const traits = ProviderTraitsUtil.get(provider);
    return traits.orgNode(short);
};

export const getProviderAdminNode = (provider: Provider): string => {
    const traits = ProviderTraitsUtil.get(provider);
    return traits.adminNode;
};

export const getProviderDisplaySpan = (provider: Provider): ReactNode => {
    switch (provider) {
        case Provider.Bitbucket:
            return <span>Bitbucket.org</span>;
        case Provider.BitbucketDataCenter:
            return (
                <span>
                    Bitbucket <b>Data Center</b>
                </span>
            );
        case Provider.Github:
            return <span>GitHub.com</span>;
        case Provider.GithubEnterprise:
            return (
                <span>
                    GitHub <b>Enterprise</b>
                </span>
            );
        case Provider.Gitlab:
            return <span>GitLab.com</span>;
        case Provider.GitlabSelfHosted:
            return (
                <span>
                    GitLab <b>Enterprise</b>
                </span>
            );

        // Expected to not be called
        case Provider.Jira:
        case Provider.JiraDataCenter:
        case Provider.Linear:
        case Provider.Confluence:
        case Provider.ConfluenceDataCenter:
        case Provider.Slack:
        case Provider.Unblocked:
        case Provider.StackOverflowTeams:
        case Provider.Notion:
        case Provider.Google:
        case Provider.GoogleDriveWorkspace:
        case Provider.Web:
        case Provider.Coda:
        case Provider.AzureDevOps:
        case Provider.BuildKite:
        case Provider.Circleci:
        case Provider.UnknownDefaultOpenApi:
            return 'Unknown';
    }
};

export const getViewInProvider = (provider: Provider | undefined): string => {
    if (!provider) {
        return 'View in browser';
    }
    return `View in ${getProviderDisplayName(provider)}`;
};

export const getProvider = (provider: string): Provider | undefined => {
    switch (provider) {
        case Provider.Bitbucket:
            return Provider.Bitbucket;
        case Provider.BitbucketDataCenter:
            return Provider.BitbucketDataCenter;
        case Provider.BuildKite:
            return Provider.BuildKite;
        case Provider.Circleci:
            return Provider.Circleci;
        case Provider.Github:
            return Provider.Github;
        case Provider.Gitlab:
            return Provider.Gitlab;
        case Provider.GitlabSelfHosted:
            return Provider.GitlabSelfHosted;
        case Provider.GithubEnterprise:
            return Provider.GithubEnterprise;
        case Provider.Jira:
            return Provider.Jira;
        case Provider.JiraDataCenter:
            return Provider.JiraDataCenter;
        case Provider.Linear:
            return Provider.Linear;
        case Provider.Confluence:
            return Provider.Confluence;
        case Provider.ConfluenceDataCenter:
            return Provider.ConfluenceDataCenter;
        case Provider.Slack:
            return Provider.Slack;
        case Provider.Unblocked:
            return Provider.Unblocked;
        case Provider.StackOverflowTeams:
            return Provider.StackOverflowTeams;
        case Provider.Notion:
            return Provider.Notion;
        case Provider.Google:
            return Provider.Google;
        case Provider.Web:
            return Provider.Web;
        case Provider.Coda:
            return Provider.Coda;
        case Provider.AzureDevOps:
            return Provider.AzureDevOps;
        case Provider.GoogleDriveWorkspace:
            return Provider.GoogleDriveWorkspace;
        case Provider.UnknownDefaultOpenApi:
            return undefined;
    }
};

export const getProviderIconSrc = (
    provider: Provider,
    color?: boolean,
    viewTheme?: 'dark' | 'light'
): IconSrc | undefined => {
    const traits = ProviderTraitsUtil.get(provider);
    return traits.iconSrc(color, viewTheme);
};

export enum ProviderAppRoute {
    Asana = 'asana',
    AzureDevOps = 'azure-dev-ops',
    Bitbucket = 'bitbucket',
    BitbucketDataCenter = 'bitbucket-data-center',
    BuildKite = 'buildkite',
    CircleCI = 'circleci',
    Confluence = 'confluence',
    ConfluenceDataCenter = 'confluence-data-center',
    GitHub = 'github',
    GithubActions = 'github-actions',
    GitHubEnterprise = 'github-enterprise',
    GitLab = 'gitlab',
    GitLabSelfHosted = 'gitlab-self-hosted',
    Google = 'google-drive',
    GoogleDriveWorkspace = 'google-drive-workspace',
    Jira = 'jira',
    JiraDataCenter = 'jira-data-center',
    Linear = 'linear',
    Notion = 'notion',
    Slack = 'slack',
    StackOverflowTeams = 'stack-overflow-teams',
    Unblocked = 'unblocked-api',
    Web = 'web',
    Coda = 'coda',
    CustomIntegration = 'custom-integration',
    Unknown = 'unknown',
}

export const getProviderAppRoute = (provider: Provider): ProviderAppRoute => {
    switch (provider) {
        case Provider.Asana:
            return ProviderAppRoute.Asana;
        case Provider.AzureDevOps:
            return ProviderAppRoute.AzureDevOps;
        case Provider.BuildKite:
            return ProviderAppRoute.BuildKite;
        case Provider.Circleci:
            return ProviderAppRoute.CircleCI;
        case Provider.Github:
            return ProviderAppRoute.GitHub;
        case Provider.Gitlab:
            return ProviderAppRoute.GitLab;
        case Provider.Bitbucket:
            return ProviderAppRoute.Bitbucket;
        case Provider.BitbucketDataCenter:
            return ProviderAppRoute.BitbucketDataCenter;
        case Provider.GithubActions:
            return ProviderAppRoute.GithubActions;
        case Provider.GithubEnterprise:
            return ProviderAppRoute.GitHubEnterprise;
        case Provider.GitlabSelfHosted:
            return ProviderAppRoute.GitLabSelfHosted;
        case Provider.Jira:
            return ProviderAppRoute.Jira;
        case Provider.JiraDataCenter:
            return ProviderAppRoute.JiraDataCenter;
        case Provider.Confluence:
            return ProviderAppRoute.Confluence;
        case Provider.ConfluenceDataCenter:
            return ProviderAppRoute.ConfluenceDataCenter;
        case Provider.StackOverflowTeams:
            return ProviderAppRoute.StackOverflowTeams;
        case Provider.Linear:
            return ProviderAppRoute.Linear;
        case Provider.Slack:
            return ProviderAppRoute.Slack;
        case Provider.Notion:
            return ProviderAppRoute.Notion;
        case Provider.Unblocked:
            return ProviderAppRoute.Unblocked;
        case Provider.CustomIntegration:
            return ProviderAppRoute.CustomIntegration;
        case Provider.Google:
            return ProviderAppRoute.Google;
        case Provider.Web:
            return ProviderAppRoute.Web;
        case Provider.Coda:
            return ProviderAppRoute.Coda;
        case Provider.GoogleDriveWorkspace:
            return ProviderAppRoute.GoogleDriveWorkspace;

        // Not expected to be called
        case Provider.Aws:
        case Provider.AwsIdentityCenter:
        case Provider.GoogleWorkspace:
        case Provider.MicrosoftEntra:
        case Provider.Okta:
        case Provider.PingOne:
        case Provider.Saml:
        case Provider.UnknownDefaultOpenApi:
            return ProviderAppRoute.Unknown;
    }
};

export const getProviderFromAppRoute = (appRouteProvider: string): Provider | undefined => {
    switch (appRouteProvider) {
        case ProviderAppRoute.GitHub:
            return Provider.Github;
        case ProviderAppRoute.GitLab:
            return Provider.Gitlab;
        case ProviderAppRoute.Bitbucket:
            return Provider.Bitbucket;
        case ProviderAppRoute.BitbucketDataCenter:
            return Provider.BitbucketDataCenter;
        case ProviderAppRoute.GithubActions:
            return Provider.GithubActions;
        case ProviderAppRoute.GitHubEnterprise:
            return Provider.GithubEnterprise;
        case ProviderAppRoute.GitLabSelfHosted:
            return Provider.GitlabSelfHosted;
        case ProviderAppRoute.Jira:
            return Provider.Jira;
        case ProviderAppRoute.JiraDataCenter:
            return Provider.JiraDataCenter;
        case ProviderAppRoute.Confluence:
            return Provider.Confluence;
        case ProviderAppRoute.ConfluenceDataCenter:
            return Provider.ConfluenceDataCenter;
        case ProviderAppRoute.StackOverflowTeams:
            return Provider.StackOverflowTeams;
        case ProviderAppRoute.Linear:
            return Provider.Linear;
        case ProviderAppRoute.Slack:
            return Provider.Slack;
        case ProviderAppRoute.Notion:
            return Provider.Notion;
        case ProviderAppRoute.Unblocked:
            return Provider.Unblocked;
        case ProviderAppRoute.Google:
            return Provider.Google;
        case ProviderAppRoute.GoogleDriveWorkspace:
            return Provider.GoogleDriveWorkspace;
        case ProviderAppRoute.Web:
            return Provider.Web;
        case ProviderAppRoute.Coda:
            return Provider.Coda;
        case ProviderAppRoute.AzureDevOps:
            return Provider.AzureDevOps;
        case ProviderAppRoute.BuildKite:
            return Provider.BuildKite;
        case ProviderAppRoute.CircleCI:
            return Provider.Circleci;
        case ProviderAppRoute.CustomIntegration:
            return Provider.CustomIntegration;
        default:
            return undefined;
    }
};

/**
 * True if, when connecting an integration for the given provider, we must warn the user that the data
 * from the provider will be shared with LLMs
 */
export const providerIntegrationConnectionRequiresDataShareWarning = (provider: Provider): boolean => {
    switch (provider) {
        case Provider.Google:
            return true;

        default:
            return false;
    }
};

/**
 * True if, when connecting an integration for the given provider, we must log into the provider first
 */
export const providerIntegrationConnectionRequiresAuth = (provider: Provider): boolean => {
    switch (provider) {
        case Provider.Aws:
        case Provider.AzureDevOps:
        case Provider.BuildKite:
        case Provider.Circleci:
        case Provider.Github:
        case Provider.GithubActions:
        case Provider.GithubEnterprise:
        case Provider.Bitbucket:
        case Provider.BitbucketDataCenter:
        case Provider.Gitlab:
        case Provider.GitlabSelfHosted:
        case Provider.StackOverflowTeams:
        case Provider.Coda:
        case Provider.ConfluenceDataCenter:
        case Provider.CustomIntegration:
        case Provider.JiraDataCenter:
        case Provider.Unblocked:
        case Provider.Web:
        case Provider.GoogleDriveWorkspace:
            return false;

        case Provider.Asana:
        case Provider.AwsIdentityCenter:
        case Provider.Confluence:
        case Provider.Google:
        case Provider.GoogleWorkspace:
        case Provider.Jira:
        case Provider.Linear:
        case Provider.MicrosoftEntra:
        case Provider.Notion:
        case Provider.Okta:
        case Provider.PingOne:
        case Provider.Slack:
        case Provider.Saml:
        case Provider.UnknownDefaultOpenApi:
            return true;
    }
};

/**
 * True if the user selects SCM repos within the external provider
 * This is true for GitHub
 */
export const providerAllowsScmSelection = (provider: Provider): boolean =>
    provider === Provider.Github || provider === Provider.GithubEnterprise;

/**
 * Whether a provider is an SCM provider.
 */
export const isScmProvider = (provider: Provider): boolean => {
    const traits = ProviderTraitsUtil.get(provider);
    return traits.isScmProvider();
};

/**
 * Whether a provider is a CI provider.
 */
export const isCiProvider = (provider: Provider): boolean => {
    const traits = ProviderTraitsUtil.get(provider);
    return traits.isCiProvider();
};

/**
 * Whether a provider is sign in capable.
 */
export const isSignInCapable = (provider: Provider): boolean => {
    switch (provider) {
        case Provider.Github:
        case Provider.GithubEnterprise:
        case Provider.Gitlab:
        case Provider.GitlabSelfHosted:
        case Provider.Bitbucket:
        case Provider.BitbucketDataCenter:
        case Provider.AzureDevOps:
        case Provider.Slack:
            return true;
        default:
            return false;
    }
};

/*
 * Whether a provider supports multiple connected instances.
 */
export const supportsMultiProvider = (provider: Provider): boolean => {
    switch (provider) {
        case Provider.Github:
        case Provider.GithubEnterprise:
        case Provider.Gitlab:
        case Provider.GitlabSelfHosted:
        case Provider.Bitbucket:
        case Provider.BitbucketDataCenter:
        case Provider.AzureDevOps:
        case Provider.CustomIntegration:
        case Provider.Jira:
        case Provider.JiraDataCenter:
        case Provider.ConfluenceDataCenter:
            return true;
        default:
            return false;
    }
};
