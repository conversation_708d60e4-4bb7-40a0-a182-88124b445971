import { API } from '@shared/api';

import { PollingDataStream } from '@shared/stores/PollingDataStream';
import { LazyValue } from '@shared/webUtils';
import { MS } from '@shared/webUtils/TimeUtils';

export class RemoteMcpToolStore {
    static readonly instance = LazyValue(() => new RemoteMcpToolStore());

    private stream = new PollingDataStream({
        pollFn: async () => {
            const response = await API.mcp.getMcpTools();
            return response.tools;
        },
        period: MS.hours(1),
    });

    readonly toolStream = this.stream;
}
