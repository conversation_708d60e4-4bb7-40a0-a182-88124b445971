@use 'layout' as *;
@use 'fonts' as *;
@use 'misc' as *;
@use 'flex' as *;
@use '@shared-styles/layout-mixin' as *;

.pull_request_info {
    display: grid;
    grid-template-columns: 100%;
    grid-template-rows: auto 1fr;
    position: relative;
    height: 100%;

    .insight_header {
        position: sticky;
        top: 0;
        z-index: 100;

        a {
            text-decoration: none;
        }
    }

    .pull_request_info__content {
        display: grid;
        grid-template-columns: minmax(0, 1fr);
        overflow: overlay;
    }

    .pull_request_info_view__header {
        @include flex-center($spacer-4);

        a {
            font-weight: $font-weight-normal;
        }

        > div {
            @include flex-center($spacer-8);
            @include ellipsis;
        }
    }

    .pull_request_description__empty_body {
        padding-top: $spacer-16;
        font-style: italic;
        opacity: 0.5;
    }

    .pull_request_description {
        position: relative;
        border-bottom-width: $border-width;
        border-bottom-style: $border-style;

        .message_view {
            border-bottom: none;
        }
    }

    .pull_request_blocks {
        margin: 0 auto;

        & > div {
            padding-left: $spacer-24;
            padding-right: $spacer-24;
        }
    }

    .pull_request_info__slack_link {
        display: block;
        margin: $spacer-24 0;
        text-align: center;
        font-size: $font-size-13;
    }

    .pull_request_info__comments {
        position: relative;
        overflow-y: overlay;
    }

    .pull_request_info__slack {
        margin-top: $spacer-24;
        margin-bottom: $spacer-24;

        .pull_request_blocks {
            max-width: none;

            & > div {
                padding-left: 0;
                padding-right: 0;
                margin-left: $spacer-24;
                margin-right: $spacer-24;
            }

            .pr_thread_container__view_container {
                margin: 0 auto;
            }
        }
    }

    .pull_request_view__tab_header {
        @include flex-center-center;
        gap: $spacer-4;
    }
}
