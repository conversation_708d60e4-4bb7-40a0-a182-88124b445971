import classNames from 'classnames';
import {
    ClipboardEvent,
    CSSProperties,
    DragEvent,
    forwardRef,
    KeyboardEvent,
    MouseEvent,
    ReactElement,
    useCallback,
    useEffect,
    useImperativeHandle,
    useMemo,
    useRef,
    useState,
} from 'react';
import {
    BaseEditor,
    createEditor,
    Descendant,
    Editor,
    Element,
    Node,
    NodeEntry,
    Path,
    Point,
    Range,
    Text,
    Transforms,
} from 'slate';
import { withHistory } from 'slate-history';
import { Editable, ReactEditor, RenderElementProps, RenderLeafProps, Slate, withReact } from 'slate-react';

import { ListsEditor as PrezlyListsEditor } from '@prezly/slate-lists';

import { faCircleXmark } from '@fortawesome/pro-solid-svg-icons/faCircleXmark';
import { faEye } from '@fortawesome/pro-solid-svg-icons/faEye';
import { faEyeSlash } from '@fortawesome/pro-solid-svg-icons/faEyeSlash';
import { faPaperPlaneTop } from '@fortawesome/pro-solid-svg-icons/faPaperPlaneTop';

import { Block } from '../../api/models';
import { Icon } from '../Icon/Icon';
import { TutorialTooltip } from '../Tutorial/TutorialTooltip';
import { AnswerPreferencesTooltip } from './AnswerPreferencesTooltip';
import { BlockTraitsMap, CustomElement, CustomText } from './CustomElementTypes';
import { ProcessHotkey, ProcessKey, ProcessShiftHotkey } from './Keyboard';
import { HandleDataTransfer } from './Media/DataTransfer';
import { MessageEditorButton } from './MessageEditorButton';
import { CustomEditor } from './MessageEditorTypes';
import { getClosestElement } from './MessageEditorUtils';
import { BlockToEditorTranslator } from './MessageTranslators/BlockToEditorTranslator';
import { EditorToBlockTranslator } from './MessageTranslators/EditorToBlockTranslator';
import { withCustomLists } from './Plugins/ListPlugin/CustomListsSchema';
import { MentionDropdown } from './Plugins/MentionPlugin/components/MentionDropdown';
import { MentionEditor } from './Plugins/MentionPlugin/CustomMentionTypes';
import { diffMentions } from './Plugins/MentionPlugin/lib/diffMentions';
import { withMention } from './Plugins/MentionPlugin/withMention';

import './MessageEditor.scss';

type ListsEditor = typeof PrezlyListsEditor;

// The way you specify the document types in Slate is unusual
// Read here for more information: https://docs.slatejs.org/concepts/12-typescript
declare module 'slate' {
    interface CustomTypes {
        Editor: BaseEditor & ReactEditor & MentionEditor & ListsEditor;
        Element: CustomElement;
        Text: CustomText;
    }
}

// Renders block elements
function RenderElement(props: RenderElementProps): ReactElement {
    const toRender = BlockTraitsMap.get(props.element.type)?.render(props);
    if (toRender) {
        return toRender;
    }

    return <div {...props.attributes}>{props.children}</div>;
}

function RenderPlaceholder(props: RenderLeafProps) {
    // Stolen from the SlateJS code.  This is how Slate renders placeholders.
    const style: CSSProperties = {
        position: 'absolute',
        pointerEvents: 'none',
        width: '100%',
        maxWidth: '100%',
        display: 'block',
        userSelect: 'none',
        textDecoration: 'none',
        top: '0',
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        textOverflow: 'ellipsis',
    };

    return (
        <span {...props.attributes}>
            <span
                className="message_editor__placeholder"
                data-slate-placeholder={true}
                style={style}
                contentEditable={false}
            >
                {props.leaf.placeholder}
            </span>
            {props.children}
        </span>
    );
}

function RenderLeaf(props: RenderLeafProps) {
    if (props.leaf.placeholder) {
        return RenderPlaceholder(props);
    }

    const classnames = classNames({
        paragraph_bold: props.leaf.bold,
        paragraph_italic: props.leaf.italic,
        paragraph_underline: props.leaf.underline,
        paragraph_code: props.leaf.code,
        paragraph_strikethrough: props.leaf.strikethrough,
        paragraph_subscript: props.leaf.subscript,
        paragraph_superscript: props.leaf.superscript,
    });

    return (
        <span {...props.attributes} className={classnames}>
            {props.children}
        </span>
    );
}

function HandleInsertBreak(editor: Editor): boolean {
    // The default break behaviour is to create a new empty editing node.
    // If the current node is a multiline-editing node, insert a line break into the current
    // node instead of creating a new node.

    const selection = editor.selection;
    if (!selection) {
        return false;
    }

    const block = Editor.above(editor, {
        match: (n) => Element.isElement(n) && Editor.isBlock(editor, n),
    });
    if (!block) {
        return false;
    }

    const leaf = Editor.node(editor, selection);

    const node = block[0];
    if (Element.isElement(node) && Editor.isBlock(editor, node) && BlockTraitsMap.get(node.type)?.isMultiline) {
        const string = Editor.string(editor, leaf[1]);

        // If this is a second linespace in a row, trim linespaces and start a new plain block.
        if (string.endsWith('\n')) {
            const deleteRange: Range = {
                anchor: { path: leaf[1], offset: string.length - 1 },
                focus: { path: leaf[1], offset: string.length },
            };
            Transforms.delete(editor, { at: deleteRange });
            Transforms.insertNodes(editor, { type: 'paragraph', children: [{ text: '' }] });
        } else {
            Transforms.insertText(editor, '\n');
        }
        return true;
    }

    return false;
}

function HandleDeleteBackward(editor: Editor): boolean {
    // The default deletion behaviour is to remove characters in the current block.  This customizes
    // the behaviour so that if you are deleting the beginning of a custom block, the block is reverted
    // to a plain paragraph block.
    const { selection } = editor;
    if (!selection || !Range.isCollapsed(selection)) {
        return false;
    }

    const match = Editor.above(editor, {
        match: (n) => Element.isElement(n) && Editor.isBlock(editor, n),
    });

    if (!match) {
        return false;
    }

    const [block, path] = match;
    if (Element.isElement(block) && BlockTraitsMap.get(block.type)?.handleDeleteBackwards) {
        return false;
    }

    const start = Editor.start(editor, path);
    if (Editor.isEditor(block) || block.type === 'paragraph' || !Point.equals(selection.anchor, start)) {
        return false;
    }

    Transforms.setNodes(editor, { type: 'paragraph' });
    return true;
}

function HandleNormalizeNode(editor: Editor, entry: NodeEntry) {
    const [node, location] = entry;

    // De-format any formatting that was applied to nodes that don't support it
    if (Text.isText(node)) {
        const element = getClosestElement(editor, location);
        if (element && !BlockTraitsMap.get(element.type)?.allowsInlineFormattedContent) {
            Transforms.setNodes(
                editor,
                {
                    bold: undefined,
                    italic: undefined,
                    underline: undefined,
                    code: undefined,
                    strikethrough: undefined,
                    superscript: undefined,
                    subscript: undefined,
                },
                { match: (n) => Text.isText(n), at: location, split: false }
            );
        }
    }

    if (!editorCanSubmit(editor.children)) {
        Transforms.setNodes(
            editor,
            {
                bold: undefined,
                italic: undefined,
                underline: undefined,
                code: undefined,
                strikethrough: undefined,
                superscript: undefined,
                subscript: undefined,
            },
            { match: (n) => Text.isText(n), at: location, split: false }
        );
    }

    // Ensure there is always a plain editable paragraph at the bottom of the document
    // If no children, just introduce one at end.
    // Editor.end will produce an exception if the Editor has no children.
    const paragraphNode: Node = { type: 'paragraph', children: [{ text: '' }] };
    if (editor.children.length > 0) {
        const child = editor.children[editor.children.length - 1];
        if (
            Element.isElement(child) &&
            Editor.isBlock(editor, child) &&
            child.type !== 'paragraph' &&
            !BlockTraitsMap.get(child.type)?.canBeLastBlock
        ) {
            Transforms.insertNodes(editor, paragraphNode, { at: [editor.children.length] });
        }
    }

    if (editor.children.length === 0) {
        Transforms.insertNodes(editor, paragraphNode);
    }
}

export const EmptyMessage: EditorContent = [
    {
        type: 'paragraph',
        children: [
            {
                text: '',
            },
        ],
    },
];

// True if we can submit the editor content
// This is true if any node has any non-whitespace text, or if we have
// a non-text node
const editorCanSubmit = (content: EditorContent): boolean => {
    return content.some((node) => {
        const isElement = Element.isElement(node);
        if (!isElement) {
            return false;
        }

        switch (node.type) {
            case 'paragraph':
                return node.children.some(
                    (child) => (Text.isText(child) && child.text.trim().length > 0) || Element.isElement(child)
                );
            default:
                return true;
        }
    });
};

// True if the editor has no content
const editorIsEmpty = (content: EditorContent): boolean => {
    if (content.length === 0) {
        return true;
    }
    if (content.length > 1) {
        return false;
    }

    const node = content[0];
    if (!Element.isElement(node) || node.type !== 'paragraph') {
        return false;
    }

    if (node.children.length === 0) {
        return true;
    }

    if (node.children.length > 1) {
        return false;
    }

    const child = node.children[0];
    if (!Text.isText(child) || Element.isElement(child)) {
        return false;
    }

    return child.text.length === 0;
};

// reset the editor content to an empty state
function resetEditor(editor: Editor): void {
    Transforms.delete(editor, {
        at: {
            anchor: Editor.start(editor, []),
            focus: Editor.end(editor, []),
        },
    });
}

function getBlocks(editorContent: EditorContent): Block[] {
    return EditorToBlockTranslator.translateToBlock(editorContent);
}

function CreateSlateEditor(): Editor {
    const editor = withReact(withHistory(createEditor()));
    const { isInline, isVoid, insertBreak, deleteBackward, normalizeNode } = editor;

    editor.isVoid = (element) => {
        const traits = BlockTraitsMap.get(element.type);
        return traits ? traits.isVoid : isVoid(element);
    };

    editor.isInline = (element) => {
        const traits = BlockTraitsMap.get(element.type);
        return traits?.isInline ? traits.isInline : isInline(element);
    };

    editor.insertBreak = () => {
        if (!HandleInsertBreak(editor)) {
            insertBreak();
        }
    };

    editor.deleteBackward = (unit: 'character' | 'word' | 'line' | 'block') => {
        if (!HandleDeleteBackward(editor)) {
            deleteBackward(unit);
        }
    };

    editor.normalizeNode = (entry: NodeEntry) => {
        HandleNormalizeNode(editor, entry);
        normalizeNode(entry);
    };

    return editor;
}

function HandlePaste(editor: CustomEditor, teamId: string | undefined, e: ClipboardEvent) {
    if (HandleDataTransfer(editor, teamId, e.clipboardData)) {
        e.preventDefault();
    }
}

function HandleDrop(editor: CustomEditor, teamId: string | undefined, e: DragEvent) {
    if (HandleDataTransfer(editor, teamId, e.dataTransfer)) {
        e.preventDefault();
    }
}

function InsertNewLine(editor: CustomEditor, e: KeyboardEvent) {
    // This shouldn't be necessary.  Slate's onDOMBeforeInput handler typically
    // handles paragraph insertion, but for some reason in IntelliJ's CEF environment,
    // Chromium isn't sending this event.  This is a hack that forces the regular Slate
    // break insertion logic whenever Enter is pressed
    editor.insertBreak();
    e.preventDefault();
}

function HandleSubmit(onSubmit: () => void, e: KeyboardEvent) {
    // Short-circuit events to submit
    onSubmit();
    e.preventDefault();
}

function hasContent(editor: CustomEditor) {
    return editor.children.length > 0;
}

function isCursorAtStart(editor: CustomEditor) {
    if (!hasContent(editor)) {
        return true;
    }
    // Check if there is a selection and if it's not collapsed
    if (editor.selection && Range.isCollapsed(editor.selection)) {
        const { anchor } = editor.selection;

        // Path to the start of the document
        const startPath = [0, 0];

        // Check if the anchor (cursor) is at the start of the document
        return Path.equals(anchor.path, startPath) && anchor.offset === 0;
    }
    return false;
}

function isCursorAtBottom(editor: Editor): boolean {
    if (!hasContent(editor)) {
        return true;
    }

    // Check if there is a selection, if it's collapsed, and if the document has nodes
    if (editor.selection && Range.isCollapsed(editor.selection) && editor.children.length > 0) {
        const { anchor } = editor.selection;
        const range = Editor.end(editor, []);
        return Point.equals(anchor, range);
    }
    return false;
}

function HandleKeyDown(
    editor: CustomEditor,
    e: KeyboardEvent,
    triggerSubmit: () => void,
    onCancel?: () => void,
    submitOnEnter?: boolean
) {
    if (
        (e.getModifierState('Control') || e.getModifierState('Meta')) &&
        e.getModifierState('Shift') &&
        ProcessShiftHotkey(editor, e)
    ) {
        return;
    } else if ((e.getModifierState('Control') || e.getModifierState('Meta')) && ProcessHotkey(editor, e)) {
        return;
    } else if (ProcessKey(editor, e)) {
        return;
    } else if (
        (e.getModifierState('Meta') || e.getModifierState('Shift') || e.getModifierState('Control')) &&
        e.key === 'Enter'
    ) {
        submitOnEnter ? InsertNewLine(editor, e) : HandleSubmit(triggerSubmit, e);
        return;
    } else if (e.key === 'Enter') {
        submitOnEnter ? HandleSubmit(triggerSubmit, e) : InsertNewLine(editor, e);
        return;
    } else if (e.key === 'Escape') {
        onCancel?.();
        e.preventDefault();
        return;
    } else if (e.key === 'ArrowUp' && !isCursorAtStart(editor)) {
        // If we're not at the top, continue with editor operations
        e.stopPropagation();
        return;
    } else if (e.key === 'ArrowDown' && !isCursorAtBottom(editor)) {
        // If there is content and we are at the bottom, continue with editor operations
        e.stopPropagation();
        return;
    }
}

function CreateDecorations(
    editor: CustomEditor,
    placeholder: string | undefined,
    hasContent: boolean
): Array<Range & { placeholder: string }> {
    // This is custom logic for creating a placeholder.  The built-in SlateJS logic decorates with a placeholder when
    // there is no text leaf nodes anywhere.  This doesn't work for our document, we can have some elements (list
    // elements in particular) that have no text, but are still content.
    if (placeholder && editor.children.length === 1 && !hasContent) {
        const start = Editor.start(editor, []);
        return [{ anchor: start, focus: start, placeholder }];
    }

    return [];
}

export interface MessageEditorForwardedRef {
    clearEditor: () => void;
    getBlocks: () => Block[];
    getMentions: () => string[];
    setBlocks: (blocks: Block[]) => void;
    focus: () => void;
}

export type EditorContent = Descendant[];

/*
    initialContent: Initial Message Block content to populate editor. *Primarily used for tests/stories*
    placeholder: Message displayed when message editor is empty
    autofocus: Focus on editor on initial render if true
*/
interface Props {
    className?: string;
    initialContent?: Block[];
    teamId?: string | undefined;
    onSubmit?: (blocks: Block[], mentions: string[]) => Promise<void> | void;
    placeholder?: string;
    autofocus?: boolean;
    onMentionAdded?: (teamMemberId: string) => void;
    onMentionDeleted?: (teamMemberId: string) => void;
    onFocusChanged?: (focused: boolean) => void;
    onCancel?: () => void;
    submitOnEnter?: boolean;
    isPrivate?: boolean;
    onSetPrivate?: (isPrivate: boolean) => void;
    isDsacEnabled?: boolean;
}

export type MessageEditorProps = Props;

export const MessageEditor = forwardRef<MessageEditorForwardedRef, Props>(function MessageEditorComponent(
    {
        className,
        initialContent,
        onSubmit,
        placeholder,
        autofocus = false,
        onMentionAdded,
        onMentionDeleted,
        teamId,
        onCancel,
        isPrivate,
        onSetPrivate,
        onFocusChanged,
        submitOnEnter = true,
        isDsacEnabled = false,
    },
    ref
) {
    const editorRef = useRef<HTMLDivElement>(null);
    const mentions = useRef<string[]>([]);
    const [canSubmit, setCanSubmit] = useState(false);
    const [isEmpty, setIsEmpty] = useState(true);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const translatedInitialData = useMemo(() => {
        if (initialContent) {
            return BlockToEditorTranslator.translateToEditor(initialContent);
        }

        return EmptyMessage;
    }, [initialContent]);

    const currentData = useRef(translatedInitialData);

    const editor = useMemo(() => withMention(withCustomLists(withReact(CreateSlateEditor()))), []);

    const onChange = useCallback(
        (editorContent: EditorContent) => {
            const newMentions = diffMentions(editorContent, mentions.current, {
                onMentionAdded,
                onMentionDeleted,
            });
            mentions.current = newMentions;

            currentData.current = editorContent;

            const canSubmit = editorCanSubmit(editorContent);
            setCanSubmit(canSubmit);

            const isEmpty = editorIsEmpty(editorContent);
            setIsEmpty(isEmpty);
        },
        [onMentionAdded, onMentionDeleted]
    );

    const onSubmitCallback = useCallback(async () => {
        if (onSubmit && canSubmit && !isSubmitting) {
            const blocks = getBlocks(currentData.current);
            const result = onSubmit(blocks, mentions.current);
            if (result) {
                setIsSubmitting(true);
                try {
                    await result;
                } finally {
                    setIsSubmitting(false);
                }
            }
        }
    }, [onSubmit, currentData, canSubmit, isSubmitting]);

    const focusEditor = useCallback(() => {
        setTimeout(() => {
            Transforms.select(editor, Editor.end(editor, []));
            ReactEditor.focus(editor);
        }, 1);
    }, [editor]);

    useEffect(() => {
        if (!!autofocus) {
            focusEditor();
        }
    }, [autofocus, focusEditor]);

    useImperativeHandle(ref, () => ({
        clearEditor: () => {
            resetEditor(editor);
            mentions.current = [];
        },
        getBlocks: () => getBlocks(currentData.current),
        getMentions: () => {
            return mentions.current;
        },
        setBlocks: (blocks: Block[]) => {
            const content = BlockToEditorTranslator.translateToEditor(blocks);
            Transforms.insertFragment(editor, content, {
                at: {
                    anchor: Editor.start(editor, []),
                    focus: Editor.end(editor, []),
                },
            });

            focusEditor();
        },
        focus: () => focusEditor(),
    }));

    const onClicked = useCallback(
        (e: MouseEvent) => {
            // When we click in the area outside the editor, but within the border, focus the editor
            ReactEditor.focus(editor);
            e.preventDefault();
            e.stopPropagation();
        },
        [editor]
    );

    const onFocused = useCallback(() => {
        onFocusChanged?.(true);

        // Whenever the editor gains focus, ensure we set a default selection.  This makes sure the mouse cursor is visible.
        if (!editor.selection) {
            Transforms.select(editor, Editor.end(editor, []));
        }
    }, [onFocusChanged, editor]);
    const tutorialType = isDsacEnabled ? 'dsacAnswers' : 'answers';

    // Current value
    return (
        <Slate editor={editor} initialValue={translatedInitialData} onChange={onChange}>
            <div className={classNames('message_editor', className)} onClick={onClicked}>
                <div className="message_editor__outline" />
                <div className="message_editor__border" />
                <div className="message_editor__actions message_editor__left_actions">
                    {isPrivate !== undefined ? (
                        <TutorialTooltip
                            tutorialType={tutorialType}
                            stepIndex={0}
                            referenceElement={({ ref: tooltipRef }) => {
                                return (
                                    <MessageEditorButton
                                        ref={tooltipRef}
                                        selected={isPrivate}
                                        onClick={() => onSetPrivate?.(!isPrivate)}
                                        title="Private Question"
                                        tabIndex={-1}
                                    >
                                        <Icon icon={isPrivate ? faEyeSlash : faEye} size="medium" />
                                    </MessageEditorButton>
                                );
                            }}
                        />
                    ) : null}

                    {teamId && (
                        <TutorialTooltip
                            tutorialType={tutorialType}
                            stepIndex={1}
                            referenceElement={({ ref: tooltipRef }) => {
                                return <AnswerPreferencesTooltip teamId={teamId} ref={tooltipRef} />;
                            }}
                        />
                    )}
                </div>

                {/* Editor */}
                <div className="message_editor__content" ref={editorRef}>
                    <Editable
                        className="message_editor__content__editable"
                        decorate={() => CreateDecorations(editor, placeholder, !isEmpty)}
                        renderElement={RenderElement}
                        renderLeaf={RenderLeaf}
                        onKeyDown={(e) => HandleKeyDown(editor, e, onSubmitCallback, onCancel, submitOnEnter)}
                        onDrop={(e) => HandleDrop(editor, teamId, e)}
                        onPaste={(e) => HandlePaste(editor, teamId, e)}
                        onFocus={onFocused}
                        onBlur={() => onFocusChanged?.(false)}
                    />

                    {teamId && <MentionDropdown parent={editorRef} editor={editor} teamId={teamId} />}
                </div>

                <div className="message_editor__actions message_editor__right_actions">
                    {onCancel ? (
                        <MessageEditorButton onClick={onCancel} title="Cancel Editing" tabIndex={-1} hideIdleBackground>
                            <Icon icon={faCircleXmark} size="small" />
                        </MessageEditorButton>
                    ) : null}
                    {onSubmit && (
                        <MessageEditorButton
                            onClick={onSubmitCallback}
                            disabled={!canSubmit || isSubmitting}
                            title="Submit"
                            tabIndex={-1}
                            hideIdleBackground
                        >
                            <Icon icon={faPaperPlaneTop} size="small" />
                        </MessageEditorButton>
                    )}
                </div>
            </div>
        </Slate>
    );
});
