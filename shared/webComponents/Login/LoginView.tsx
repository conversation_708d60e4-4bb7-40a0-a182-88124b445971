import { ReactNode, useMemo } from 'react';

import { Provider } from '@shared/api/models';

import { useStream } from '@shared/stores/DataCacheStream';
import { TeamInviteModel } from '@shared/stores/TeamInviteStore';
import { useStore } from '@shared/stores/useStore';
import { Icon } from '@shared/webComponents/Icon/Icon';
import { ProviderTraitsUtil } from '@shared/webUtils/ProviderTraits/ProviderTraitsUtil';

import unblocked from '@clientAssets/unblockedColor.svg';

import { StateIcon } from '../StateIcon/StateIcon';
import { LoginList } from './LoginList';
import { LoginStoreTraits } from './LoginStoreTraits';

import './LoginView.scss';

export type LoginViewVariant = 'tryUnblocked' | 'connectSlackAccount';
interface Props {
    variant?: LoginViewVariant;
    teamId?: string;
    teamInvite?: TeamInviteModel;
    disclaimer?: ReactNode;
    securityBumper?: ReactNode;
    inviteBlock?: ReactNode;
    focusedProvider?: Provider;
    completionUrl?: string;
}

export const LoginView = ({
    variant,
    teamId,
    teamInvite,
    disclaimer,
    securityBumper,
    inviteBlock,
    focusedProvider,
    completionUrl,
}: Props) => {
    const store = useStore(LoginStoreTraits, { teamId, inviteId: teamInvite?.inviteId });
    const loginProvidersState = useStream(() => store.state, [store]);
    const loginOptionCount =
        loginProvidersState && loginProvidersState.$case === 'login' ? loginProvidersState.options?.length : 0;

    const loginTitle = useMemo(() => {
        if (loginProvidersState?.$case === 'error') {
            return `Unable to sign in. Please try again.`;
        }
        if (teamInvite?.team) {
            return `Join ${teamInvite.team.displayName} on Unblocked`;
        }
        switch (variant) {
            case 'tryUnblocked':
                return 'Log in to try Unblocked';

            case 'connectSlackAccount':
                return 'Join your team on Unblocked';
        }

        return 'Welcome back to Unblocked';
    }, [teamInvite, variant, loginProvidersState]);

    const loginSubtitle = useMemo(() => {
        if (loginProvidersState?.$case === 'error') {
            const errorString = loginProvidersState.errorLoginOption
                ? ` with ${ProviderTraitsUtil.get(loginProvidersState.errorLoginOption.provider.provider).shortenedDisplayName}`
                : '';
            return `We're sorry, Unblocked encountered an error attempting to connect${errorString}. Please try signing in again.`;
        }

        if (teamInvite) {
            return `Get better answers when you create an Unblocked account. ${loginOptionCount > 1 ? 'Start by signing in with one of the options below.' : 'Start by signing in below.'}`;
        }
        switch (variant) {
            case 'tryUnblocked':
                return "You'll be able to select from a list of our sample projects and ask questions after you log in.";

            case 'connectSlackAccount':
                return 'Get better answers when you create an Unblocked account and connect it with your Slack acccount.';
        }
        return null;
    }, [variant, teamInvite, loginOptionCount, loginProvidersState]);

    const icon = useMemo(() => {
        if (loginProvidersState?.$case === 'error') {
            return <StateIcon className="error_icon" state="error" size="medium" />;
        }
        if (teamInvite?.team) {
            return <Icon className="login__team_icon" icon={teamInvite.team} size="medium" />;
        }
        return <Icon className="unblocked_icon" icon={unblocked} size="medium" />;
    }, [loginProvidersState, teamInvite]);

    return (
        <div className="login">
            <section>
                {icon}
                <div className="login__title">{loginTitle}</div>
                {loginSubtitle && <div className="login__subtitle">{loginSubtitle}</div>}
                <h3>Log in with</h3>
                <LoginList
                    teamId={teamId}
                    inviteId={teamInvite?.inviteId}
                    focusedProvider={focusedProvider}
                    footer={
                        <>
                            {inviteBlock}
                            {securityBumper}
                            {disclaimer}
                        </>
                    }
                    completionUrl={completionUrl}
                />
            </section>
        </div>
    );
};
