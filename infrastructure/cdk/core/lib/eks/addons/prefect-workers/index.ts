import * as blueprints from '@aws-quickstart/eks-blueprints';
import {
    HelmAddOn,
    ClusterInfo,
    HelmAddOnUserProps,
    HelmAddOnProps,
    CertManagerAddOn,
    Values,
} from '@aws-quickstart/eks-blueprints';
import { dependable, readYamlDocument, supportsALL } from '@aws-quickstart/eks-blueprints/dist/utils';
import { Duration } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as path from 'path';
import { merge } from 'ts-deepmerge';

/**
 * User provided options for the Helm Chart
 */
export interface PrefectWorkersAddOnProps extends HelmAddOnUserProps {
    environment: string;
}

/**
 * Default props to be used when creating the Helm chart
 */
const defaultProps: HelmAddOnProps & PrefectWorkersAddOnProps = {
    name: 'blueprints-prefect-worker-addon',
    namespace: 'prefect',
    chart: 'prefect-worker',
    version: '2025.5.8204048',
    release: 'prefect-worker',
    repository: 'https://prefecthq.github.io/prefect-helm',
    values: {},
    environment: 'dev',
};

@supportsALL
export class PrefectWorkers extends HelmAddOn {
    readonly options: PrefectWorkersAddOnProps;

    constructor(version: string, props?: PrefectWorkersAddOnProps) {
        super({ ...defaultProps, ...props });
        this.options = this.props as PrefectWorkersAddOnProps;

        this.options.version = version;

        // Load values file for target environment
        const valuesDir = path.join(__dirname, 'values/');
        const valuesFile = readYamlDocument(path.join(valuesDir, `${this.options.environment}.yaml`));
        this.options.values = blueprints.utils.loadYaml(valuesFile);
    }

    @dependable(CertManagerAddOn.name)
    deploy(clusterInfo: ClusterInfo): Promise<Construct> {
        // Merge and render values
        let values: Values = populateValues(this.options);
        values = merge(values, this.props.values ?? {});

        // Deploy chart
        const chart = this.addHelmChart(clusterInfo, values, true, true, Duration.minutes(5));
        return Promise.resolve(chart);
    }
}

/**
 * populateValues populates the appropriate values used to customize the Helm chart
 * @param helmOptions User provided values to customize the chart
 */
function populateValues(helmOptions: PrefectWorkersAddOnProps): Values {
    const values = helmOptions.values ?? {};
    return values;
}
