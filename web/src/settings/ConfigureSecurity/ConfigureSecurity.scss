@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;
@use 'flex' as *;

.configure_security {
    .toggle_row {
        border-bottom: $border-style $border-width themed($border);
        align-items: normal;

        .toggle {
            flex: none;
        }

        &:first-of-type {
            padding-top: $spacer-16;
        }
    }

    .configure_dsac_row {
        &.configure_dsac_row__enabled {
            border-bottom: none;
            padding-bottom: $spacer-16;

            ~ .configure_dsac {
                border-bottom: $border-style $border-width themed($border);
                padding-bottom: $spacer-40;
            }
        }
    }
}
