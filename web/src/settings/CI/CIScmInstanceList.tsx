import { useCallback, useMemo } from 'react';

import { CIScmInstance } from '@shared/api/generatedApi';

import { CIScmInstanceStore } from '@shared/stores/CIScmInstanceStore';
import { useModalContext } from '@shared/webComponents/Modal/ModalContext';
import { Row } from '@shared/webComponents/Row/Row';
import { RowsList } from '@shared/webComponents/Rows/RowsList';
import { StringsHelper } from '@shared/webUtils';
import { ProviderTraitsUtil } from '@shared/webUtils/ProviderTraits/ProviderTraitsUtil';
import { Button } from '@web/components';

import { faArrowUpRightFromSquare } from '@fortawesome/pro-solid-svg-icons/faArrowUpRightFromSquare';

import { CISettingsRepoDialog } from './CISettingsRepoDialog';
import { RequestPermissionsDialog } from './RequestPermissionsDialog';

import './CIScmInstanceList.scss';

interface Props {
    store: CIScmInstanceStore;
    instances: CIScmInstance[];
}

export const CIScmInstanceList = ({ instances, store }: Props) => {
    return (
        <RowsList className="ci_scm_instance_list">
            {instances.map((instance) => (
                <ScmInstanceRow key={instance.scmInstallationId} instance={instance} store={store} />
            ))}
        </RowsList>
    );
};

const ScmInstanceRow = ({ instance, store }: { instance: CIScmInstance; store: CIScmInstanceStore }) => {
    const traits = ProviderTraitsUtil.get(instance.provider);
    const { openModal } = useModalContext();
    const description = useMemo(() => {
        if (instance.appPermissionsUrl) {
            return `A ${traits.adminNode} first needs to accept new permissions.`;
        }
        if (instance.repoMode === 'all') {
            return `All repositories enabled.`;
        }
        if (!instance.selectedRepoCount) {
            return 'No repositories enabled.';
        }
        if (instance.selectedRepoCount === instance.repoCount) {
            return `All ${StringsHelper.countOf({ singular: 'repository', plural: 'repositories' }, instance.repoCount)} enabled.`;
        }
        return `${instance.selectedRepoCount} out of ${StringsHelper.countOf({ singular: 'repository', plural: 'repositories' }, instance.repoCount)} enabled.`;
    }, [instance, traits.adminNode]);

    const onAcceptPermissions = useCallback(
        (appPermissionsUrl: string) => {
            window.open(appPermissionsUrl, '_blank');
            store.queueRefreshOnFocus();
        },
        [store]
    );

    const onRequestPermissions = useCallback(
        (appPermissionsUrl: string) => {
            openModal(<RequestPermissionsDialog instance={instance} appPermissionsUrl={appPermissionsUrl} />);
        },
        [instance, openModal]
    );

    const action = useMemo(() => {
        const appPermissionsUrl = instance.appPermissionsUrl;

        // does not require permissions:
        if (!appPermissionsUrl) {
            return (
                <Button
                    variant="tertiary"
                    size="xs"
                    onClick={() => {
                        openModal(
                            <CISettingsRepoDialog
                                store={store.getRepoStore(instance.scmInstallationId)}
                                instance={instance}
                            />
                        );
                    }}
                >
                    Select Repositories
                </Button>
            );
        }

        // requires permissions:
        return (
            <Button
                iconLocation="end"
                size="xs"
                icon={faArrowUpRightFromSquare}
                iconSize={10}
                onClick={
                    instance.canAcceptAppPermissions
                        ? () => onAcceptPermissions(appPermissionsUrl)
                        : () => onRequestPermissions(appPermissionsUrl)
                }
            >
                Accept Permissions
            </Button>
        );
    }, [instance, onAcceptPermissions, onRequestPermissions, openModal, store]);

    return (
        <Row
            className="ci_scm_instance_row"
            header={instance.displayName}
            description={description}
            icon={instance}
            iconSize="xxLarge"
            rightAction={action}
        />
    );
};
