import { useNavigate } from 'react-router-dom';

import { Plan, PlanBillingInfo } from '@shared/api/models';

import { DashboardUrls, NumberUtils, StringsHelper } from '@shared/webUtils';
import DateTime from '@shared/webUtils/DateTime';
import { getPlanBillingRateLabel } from '@shared/webUtils/PlanHelpers';
import { Button } from '@web/components/Button/Button';

import './OrgBilling.scss';

interface Props {
    teamId: string;
    currentPlan: Plan;
    billingInfo: PlanBillingInfo;
}

export const BillingPlanDetails = ({ teamId, currentPlan, billingInfo }: Props) => {
    const navigate = useNavigate();

    return (
        <div className="billing_plan_details">
            <div>
                <h3>Plan Details</h3>
                <div className="billing_plan_details__content">
                    <span>{currentPlan.displayName} plan</span>
                    <span>{currentPlan.rate ? `, billed ${getPlanBillingRateLabel(currentPlan.rate)}` : null}</span>
                </div>
                {currentPlan.seats ? (
                    <div className="billing_plan_details__content">
                        {currentPlan.seats} {StringsHelper.count({ singular: 'license' }, currentPlan.seats)}{' '}
                        {!!currentPlan.filledSeats ? (
                            <>
                                ({currentPlan.filledSeats === currentPlan.seats ? 'All' : currentPlan.filledSeats}{' '}
                                allocated)
                            </>
                        ) : null}
                    </div>
                ) : null}
                {billingInfo.nextCycleStartsAt && billingInfo.nextCycleCharge ? (
                    <div className="billing_plan_details__content">
                        Next Bill:{' '}
                        {NumberUtils.dollarValue(billingInfo.nextCycleCharge, { inCents: true, currency: 'USD' })} on{' '}
                        {DateTime.shortPretty(billingInfo.nextCycleStartsAt, true)}
                    </div>
                ) : null}
            </div>
            <Button onClick={() => navigate(DashboardUrls.orgPlans(teamId))} variant="tertiary" size="small">
                Change Plan
            </Button>
        </div>
    );
};
