import classNames from 'classnames';
import { HTMLAttributes, ReactNode } from 'react';

import { HoverTooltip } from '@shared/webComponents/Tooltip/HoverTooltip';

import './HeaderCell.scss';

interface Props extends HTMLAttributes<HTMLSpanElement> {
    tooltipContent?: ReactNode;
    tooltipOffset?: number;
}

export function HeaderCell({ children, className, tooltipContent, tooltipOffset, ...props }: Props) {
    if (tooltipContent) {
        return (
            <HoverTooltip
                placement="top"
                modal
                className="header_cell__tooltip"
                header={({ ref }) => (
                    <span className={classNames('header_cell', 'header_cell__with_tooltip', className)} {...props}>
                        <span ref={ref}>{children}</span>
                    </span>
                )}
                crossOffset={tooltipOffset}
            >
                {tooltipContent}
            </HoverTooltip>
        );
    }

    return (
        <span className={classNames('header_cell', className)} {...props}>
            {children}
        </span>
    );
}
