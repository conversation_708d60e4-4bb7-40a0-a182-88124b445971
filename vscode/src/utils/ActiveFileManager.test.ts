import { Subscription } from 'xstream';

import { MockWorkspace } from '@vscode/mocks/MockWorkspace';

import { MockTextEditor } from '../mocks/MockTextEditor';
import { MockWindow } from '../mocks/MockWindow';
import { ActiveFileManager } from './ActiveFileManager';

const initialFile = '/path/to/file.txt';

let activeFileManager: ActiveFileManager;
let mockWindow: MockWindow;
let mockWorkspace: MockWorkspace;
let initialEditor: MockTextEditor;

let activeFileSpy: jest.Mock;
let activeRangeSpy: jest.Mock;

let activeFileSub: Subscription;
let activeRangeSub: Subscription;

beforeEach(async () => {
    jest.useFakeTimers();

    mockWindow = new MockWindow();
    mockWorkspace = new MockWorkspace();
    initialEditor = mockWindow.addTextEditor(initialFile, 'file');
    activeFileManager = new ActiveFileManager(mockWindow, mockWorkspace);

    activeFileSpy = jest.fn();
    activeRangeSpy = jest.fn();

    activeFileSub = activeFileManager.activeFileStream.subscribe({ next: activeFileSpy });
    activeRangeSub = activeFileManager.activeRangeStream.subscribe({ next: activeRangeSpy });

    // Wait for steady state
    jest.advanceTimersByTime(1000);
});

afterEach(() => {
    jest.useRealTimers();

    activeFileSub.unsubscribe();
    activeRangeSub.unsubscribe();
});

test('Has correct initial state', async () => {
    expect(activeFileManager.activeFile).toEqual(initialFile);
    expect(activeFileSpy).toHaveBeenCalledTimes(1);
    expect(activeFileSpy).toHaveBeenNthCalledWith(1, initialFile);
    expect(activeRangeSpy).toHaveBeenCalledTimes(1);
    expect(activeRangeSpy).toHaveBeenNthCalledWith(1, {
        filePath: initialFile,
        visibleRange: {
            start: MockTextEditor.initialVisibleRange.start.line,
            end: MockTextEditor.initialVisibleRange.end.line,
        },
    });
});

test('Updating editor triggers stream update', async () => {
    expect(activeFileSpy).toHaveBeenCalledTimes(1);

    // Add a new focused editor, this should not cause any update until the debounce time expires
    mockWindow.addTextEditor('/path/to/otherfile.txt', 'file');

    jest.advanceTimersByTime(10);
    expect(activeFileSpy).toHaveBeenCalledTimes(1);

    jest.advanceTimersByTime(10);
    expect(activeFileSpy).toHaveBeenCalledTimes(1);

    // Advance beyond the debounce time, ensure that the file stream updates
    jest.advanceTimersByTime(1000);
    expect(activeFileSpy).toHaveBeenCalledTimes(2);
    expect(activeFileSpy).toHaveBeenNthCalledWith(2, '/path/to/otherfile.txt');

    // Re-focus the initial editor and verify it becomes the active file
    mockWindow.updateActiveEditor(initialEditor);
    expect(activeFileSpy).toHaveBeenCalledTimes(2);

    jest.advanceTimersByTime(1000);
    expect(activeFileSpy).toHaveBeenCalledTimes(3);
    expect(activeFileSpy).toHaveBeenNthCalledWith(3, initialFile);
});

test('Skips non-file editors', async () => {
    // This ensures that focusing on non-file editors (like the terminal editors) is ignored

    // Add a new focused editor, this should not cause any update until the debounce time expires
    mockWindow.addTextEditor('garbage', 'notafile');

    // Advance beyond the debounce time, ensure that the file stream updates
    jest.advanceTimersByTime(1000);
    expect(activeFileSpy).toHaveBeenCalledTimes(2);
    expect(activeFileSpy).toHaveBeenNthCalledWith(2, undefined);
});

test('Maintains focus on non-editor webviews', async () => {
    expect(activeFileSpy).toHaveBeenCalledTimes(1);

    // This mimics a webview taking focus while the last-edited file is still visible, in this case we
    // still show the original editor as active, so no actions should have taken place
    mockWindow.updateActiveEditor(undefined);
    jest.advanceTimersByTime(1000);
    expect(activeFileSpy).toHaveBeenCalledTimes(1);
    expect(activeFileManager.activeFile).toEqual(initialFile);

    // Now mimic that the original file is no longer visible -- this mimics closing or backgrounding the
    // original editor, and the active file should clear at this point
    mockWindow.updateVisibleEditors([]);
    jest.advanceTimersByTime(1000);
    expect(activeFileSpy).toHaveBeenCalledTimes(2);
    expect(activeFileManager.activeFile).toEqual(undefined);
});

test('Updating view area triggers stream update', async () => {
    expect(activeFileSpy).toHaveBeenCalledTimes(1);
    expect(activeRangeSpy).toHaveBeenCalledTimes(1);

    // Update the visible ranges a few times, we do *not* expect any range updates until the
    // debounce timeout has passed
    initialEditor.updateVisibleRangeLines(50, 60);

    jest.advanceTimersByTime(10);
    expect(activeRangeSpy).toHaveBeenCalledTimes(1);

    jest.advanceTimersByTime(10);
    expect(activeRangeSpy).toHaveBeenCalledTimes(1);

    initialEditor.updateVisibleRangeLines(50, 60);

    jest.advanceTimersByTime(10);
    expect(activeRangeSpy).toHaveBeenCalledTimes(1);

    // Advance beyond the debounce time, ensure that the range stream updates but the file stream doesn't
    jest.advanceTimersByTime(1000);
    expect(activeFileSpy).toHaveBeenCalledTimes(1);
    expect(activeRangeSpy).toHaveBeenCalledTimes(2);

    expect(activeRangeSpy).toHaveBeenNthCalledWith(2, {
        filePath: initialFile,
        visibleRange: { start: 50, end: 60 },
    });
});
