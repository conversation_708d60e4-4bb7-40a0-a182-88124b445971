import { <PERSON><PERSON><PERSON><PERSON> } from '@shared/ide/HubAPI/HubAPI';
import { configureIDELogger } from '@shared/ide/utils/IDELogger';
import { McpServer } from '@shared/mcp/McpServer';
import { AuthStore } from '@shared/stores/AuthStore';
import { WindowFocusStream } from '@shared/stores/WindowFocusStream';
import { ClientWorkspace } from '@shared/webComponents/ClientWorkspace/ClientWorkspace';
import { StreamAsyncFetcher } from '@shared/webUtils';
import { logger, setBaseLoggerVerbosity } from '@shared/webUtils/log/BaseLogger';
import { MS } from '@shared/webUtils/TimeUtils';

import { ClientType } from '../../common/build/generated/source/proto/main/ts_proto/ClientType';
import { ConfigureAPI } from './api';
import { McpClientWorkspace } from './ClientWorkspace';
import { EnsureDesktopAppRunning } from './McpServerUtils';

const log = logger('server');

async function AwaitAuthentication(timeout: number) {
    if (AuthStore.get().isAuthenticated) {
        log.info('Tool is authenticated');
        return;
    }

    log.info('Awaiting authentication');
    await EnsureDesktopAppRunning();
    await HubAPI.activate();

    // Wait for awhile for the user to authenticate in the desktop app
    await StreamAsyncFetcher(
        AuthStore.get().isAuthenticatedStream.filter((isAuth) => isAuth),
        (t) => t,
        timeout
    );
}

async function main() {
    try {
        await ConfigureAPI();

        configureIDELogger('mcp', { disableConsoleLogging: true });
        setBaseLoggerVerbosity(true); // FIXME remove

        log.info('Starting up');

        ClientWorkspace.instance().setProvider(new McpClientWorkspace());

        try {
            await AuthStore.get().setupAuth();
        } catch {}

        await HubAPI.initialize(ClientType.MCP, {
            processAuthTokens: true,
        });

        log.info('Hub initialized');

        // If the desktop is not authenticated, pop it open so the user knows to log in
        await AwaitAuthentication(MS.seconds(0));

        WindowFocusStream.updateFocusedState(true);

        const mcpServer = new McpServer({
            authRequired: () => AwaitAuthentication(MS.seconds(30)),
        });
        await mcpServer.initialize();

        log.info('MCP server started');
    } catch (error) {
        log.error('Error in MCP server:', error);
        process.exit(1);
    }
}

main();
