import fetch, { RequestInfo as NodeRequestInfo, RequestInit as NodeRequestInit } from 'node-fetch';

import {
    BaseAPI,
    BaseAPIConfig,
    FetchAPI,
    FetchParams,
    generateTokenRefreshMiddleware,
    RequestContext,
} from '@shared/api';

import { CreateSystemProxyAgent } from '@shared/node/proxy/SystemProxySettings';

import { McpTokenProvider } from './TokenProvider';

export async function ConfigureAPI() {
    const agent = await CreateSystemProxyAgent();

    const fetchApi: FetchAPI = async (input, init): Promise<Response> => {
        // Functionally no difference between types. Just poor typing from NodeFetch
        const wrappedInput = input as NodeRequestInfo;
        const wrappedInit = init as NodeRequestInit;

        const realInit: NodeRequestInit = {
            ...wrappedInit,
            highWaterMark: 50 * 1024 * 1024,
            agent,
        };

        const response = await fetch(wrappedInput, realInit);
        return response as unknown as Response;
    };
    const fetchSetupMiddleware = (context: RequestContext): Promise<FetchParams | void> => {
        return Promise.resolve({
            url: context.url,
            init: {
                ...context.init,
                mode: 'cors',
                credentials: 'include',
            },
        });
    };

    const tokenRefreshMiddleware = async (context: RequestContext): Promise<FetchParams | void> => {
        return generateTokenRefreshMiddleware()(context);
    };

    const WebAPIConfig: BaseAPIConfig = {
        fetchApi,
        tokenProvider: McpTokenProvider,
        refreshTeams: async () => {},
        additionalMiddleware: [{ pre: fetchSetupMiddleware }, { pre: tokenRefreshMiddleware }],
    };

    BaseAPI.setup(WebAPIConfig);
}
