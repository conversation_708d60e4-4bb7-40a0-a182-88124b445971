MAKEFILE_DIR := $(dir $(abspath $(firstword $(MAKEFILE_LIST))))

generate-shared-api:
	$(MAKEFILE_DIR)/../gradlew :api:openApiGenerateShared :api:openApiExtraGenerateShared
.PHONY: generate-shared-api

generate-message-api:
	$(MAKE) -C $(MAKEFILE_DIR)../common generate-common-protos
.PHONY: generate-message-api

generate-api:
	cd $(MAKEFILE_DIR).. && ./gradlew :api:openApiGenerateShared :api:openApiExtraGenerateShare :common:generateProto
.PHONY: generate-api

build/.fast-client-dependencies: $(shell find private.yml extra.yml components extraComponents ../common/src/main/resources/protos ../package-lock.json ../openapi/template/typescript-fetch)
	$(MAKE) generate-shared-api
	mkdir -p build
	touch build/.fast-client-dependencies

# A faster make dependency that only runs when API spec, proto spec, or package.json changes
fast-client-dependencies: build/.fast-client-dependencies
.PHONY: fast-client-dependencies
