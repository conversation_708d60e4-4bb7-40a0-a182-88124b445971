type: object
properties:
  id:
    $ref: "../components/ApiResourceId.yml"
  teamId:
    $ref: "../components/ApiResourceId.yml"
  repoId:
    $ref: "../components/ApiResourceId.yml"
  provider:
    $ref: "../components/Provider.yml"
  author:
    $ref: "../components/TeamMember.yml"
  isAuthorCurrentPerson:
    description: True if the team member is the current user, false otherwise.
    type: boolean
  authorTeamMemberId:
    $ref: "../components/ApiResourceId.yml"
  state:
    description: Current state of the PR in the SCM provider.
    type: string
    enum: [open, closed, merged]
    minLength: 1
    maxLength: 500
  isStale:
    description: |
      True if the open PR is considered to be stale.
      This is a heuristic defined by the service derived from properties of the SCM pull request.

      For example, a PR might be considered stale if the PR is more than 60 days old,
      and there has been no activity for 30 days.
    type: boolean
  createdAt:
    type: string
    format: date-time
  mergedAt:
    type: string
    format: date-time
  mergeCommitSha:
    $ref: "../components/SHA.yml"
  htmlUrl:
    description: Url of the external pull request web page in the SCM provider.
    type: string
    minLength: 1
    maxLength: 5000
  title:
    description: Pull request title.
    type: string
    minLength: 1
    maxLength: 1024
  number:
    description: External pull request number from the SCM provider.
    type: integer
    format: int32
    minimum: 0
    maximum: 1000000
  description:
    ## Deprecated: Do not use in clients, use descriptionMessage instead
    ## TODO remove this field
    $ref: "../components/PullRequestDescription.yml"
  descriptionMessage:
    $ref: "../components/Message.yml"
  commentCount:
    description: Number of comments in the PR
    type: integer
    format: int32
    minimum: 0
    maximum: 1000000
  ingestionInProgress:
    type: boolean
  hasSlackThreads:
    type: boolean
  teamParticipants:
    type: array
    items:
      $ref: "../components/TeamMember.yml"
    description: |
      List of topic IDs that are associated with the pull request.
  links:
    $ref: "../components/PullRequestLinks.yml"
  experts:
    type: array
    items:
      $ref: "../components/TeamMember.yml"
    description: |
      List of topic IDs that are associated with the pull request.
  archivedAt:
    type: string
    format: date-time
  archivedBy:
    $ref: "../components/ApiResourceId.yml"
  isDeleted:
    type: boolean
required:
  - id
  - teamId
  - repoId
  - provider
  - author
  - isAuthorCurrentPerson
  - authorTeamMemberId
  - state
  - isStale
  - createdAt
  - htmlUrl
  - title
  - number
  - descriptionMessage
  - commentCount
  - teamParticipants
  - links
  - experts
