type: object
properties:
  externalId:
    type: string
    description: |
      External ID of the repo, used for uniquely identifying a repo in the SCM.
    minLength: 1
    maxLength: 500
  fullName:
    type: string
    description: |
      Full name of the repo, like `ownerName/repoName`.
    minLength: 1
    maxLength: 500
  ownerName:
    type: string
    description: |
      Name of the repo owner.
    minLength: 1
    maxLength: 500
  repoName:
    type: string
    description: |
      Name of the repo.
    minLength: 1
    maxLength: 500
  webUrl:
    type: string
    description: |
      Web HTML url of the repo in the SCM, like `https://github.com/org/repo`.
    minLength: 1
    maxLength: 5000
  httpUrl:
    type: string
    description: |
      Canonical HTTP url of the repo in the SCM, like `https://github.com/org/repo.git`.
    minLength: 1
    maxLength: 5000
  sshUrl:
    type: string
    description: |
      Canonical SSH url of the repo in the SCM, like `ssh://github.com/org/repo.git`.
    minLength: 1
    maxLength: 5000
  scpUrl:
    type: string
    description: |
      Canonical SCP url of the repo in the SCM, like `**************:org/repo.git`.
      Optional, because SCP urls can only be constructed for urls with default ports.
    minLength: 1
    maxLength: 5000
  createdAt:
    type: string
    format: date-time
    description: |
      Original SCM repo creation timestamp, not date added to Unblocked.
  lastActiveAt:
    type: string
    format: date-time
    description: |
      Typically timestamp of latest commit pushed to the repo.
  isFork:
    type: boolean
    description: |
      Whether the repo is a fork.
  isEmpty:
    type: boolean
    description: |
      Whether the repo is empty, meaning it has zero Git commits.
required:
  - fullName
  - ownerName
  - repoName
  - webUrl
  - httpUrl
  - sshUrl
