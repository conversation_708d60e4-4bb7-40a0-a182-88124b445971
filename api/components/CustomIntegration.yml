type: object
properties:
  id:
    $ref: "./ApiResourceId.yml"
  name:
    type: string
    description: |
      A brief identifier for the collection.
    example: "Apollo"
  iconUrl:
    type: string
    description: |
      A URL of a square image used to visually represent document references from the collection in the Unblocked UI. Preferred image formats are SVG, PNG, and JPG. The URL can also be a data URL.
    example: "https://my.company.com/apollo/logo.svg"
  description:
    type: string
    description: |
      A sentence defining the document type within this collection, aiding the language model in grasping the content's essence.
    example: "Documents from the Apollo customer support tool including support responses."
  lastDocumentModifiedAt:
    type: string 
    format: date-time 
    description: |
      Date of when the last document was modified (added/removed/updated)
  documentCount:
    type: integer
    format: int32
    minimum: 0
    maximum: 10000000
  latestDocumentEvents:
    description: |
      Limited list of the latest events that modified documents
    type: array
    items:
      $ref: ./CustomDocumentEvent.yml

required:
  - id
  - name
  - iconUrl
  - description
  - documentCount