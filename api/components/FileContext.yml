type: object
description: |
  A file that is in context.

  The `lineRange` and `partialContent` properties may not always be available from the IDE,
  particularly for background tabs.
properties:
  repoId:
    $ref: "./ApiResourceId.yml"
  filePath:
    description: |
      The path for this file, relative to the repo root.
    type: string
    minLength: 1
    maxLength: 5000
  isVisible:
    description: |
      True if this file's content is visible,
      false otherwise meaning that the file is in a background tab.
    type: boolean
  isActive:
    description: |
      True if this file is the active file (the one receiving edit input),
      false otherwise.
    type: boolean
  lastViewedAt:
    description: |
      The last date when this panel became visible.
      Not provided if this panel has never been visible.
    type: string
    format: date-time
  lineRange:
    $ref: "./LineRange.yml"
  partialContent:
    description: |
      Base 64 encoded and compressed file content for the `lineRange`.
    type: string
    minLength: 1
    maxLength: 5000
  selectedRange:
    $ref: "./SelectedRange.yml"
required:
  - repoId
  - filePath
  - isVisible
  - isActive
