type: object
properties:
  id:
    $ref: "./ApiResourceId.yml"
  htmlUrl:
    description: Url of the external pull request web page in the SCM provider.
    type: string
    minLength: 1
    maxLength: 5000
  title:
    description: Pull request title.
    type: string
    minLength: 1
    maxLength: 1024
  number:
    description: External pull request number from the SCM provider.
    type: integer
    format: int32
    minimum: 0
    maximum: 1000000
required:
  - id
  - htmlUrl
  - title
  - number
