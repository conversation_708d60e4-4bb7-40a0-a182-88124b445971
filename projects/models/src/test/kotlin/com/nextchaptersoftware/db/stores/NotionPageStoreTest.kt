package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class NotionPageStoreTest : DatabaseTestsBase() {
    private val store = Stores.notionPageStore

    private val pageIdA1 = UUID.randomUUID()
    private val pageIdA2 = UUID.randomUUID()
    private val pageIdA3 = UUID.randomUUID()
    private val pageIdB1 = UUID.randomUUID()
    private val pageIdB2 = UUID.randomUUID()
    private val pageIdB3 = UUID.randomUUID()

    @Test
    fun getNextPagesToIngest() = suspendingDatabaseTest {
        val installationAId = makeInstallation().idValue
        val installationBId = makeInstallation().idValue

        // Sanity check that there are no pages to ingest
        assertThat(store.getNextPagesToIngest(installationId = installationAId, limit = 1)).isEmpty()
        assertThat(store.getNextPagesToIngest(installationId = installationBId, limit = 1)).isEmpty()

        // Set up the pages, then check that the correct pages that need to be ingested are returned in the correct order
        store.markForIngestion(installationId = installationAId, notionPageId = pageIdA1, priority = null)
        store.markForIngestion(installationId = installationAId, notionPageId = pageIdA2, priority = 10)
        store.markForIngestion(installationId = installationAId, notionPageId = pageIdA3, priority = 20)
        store.markForIngestion(installationId = installationBId, notionPageId = pageIdB1, priority = null)
        store.markForIngestion(installationId = installationBId, notionPageId = pageIdB2, priority = null)
        store.markForIngestion(installationId = installationBId, notionPageId = pageIdB3, priority = null)
        store.markAsErrored(installationId = installationBId, notionPageId = pageIdB1)
        store.markAsIngested(installationId = installationBId, notionPageId = pageIdB3, lastEditedTime = Instant.nowWithMicrosecondPrecision())
        assertThat(store.getNextPagesToIngest(installationId = installationAId, limit = 1).single().notionPageId).isEqualTo(pageIdA3)
        assertThat(store.getNextPagesToIngest(installationId = installationBId, limit = 1).single().notionPageId).isEqualTo(pageIdB2)

        // Mark a page as errored during ingestion, then check the correct next page to be ingested is returned
        store.markAsErrored(installationId = installationAId, notionPageId = pageIdA3)
        assertThat(store.getNextPagesToIngest(installationId = installationAId, limit = 1).single().notionPageId).isEqualTo(pageIdA2)
        assertThat(store.getNextPagesToIngest(installationId = installationBId, limit = 1).single().notionPageId).isEqualTo(pageIdB2)

        // Mark a page as ingested, then check that we're done ingesting for that installation
        store.markAsIngested(installationId = installationBId, notionPageId = pageIdB2, lastEditedTime = Instant.nowWithMicrosecondPrecision())
        assertThat(store.getNextPagesToIngest(installationId = installationAId, limit = 1).single().notionPageId).isEqualTo(pageIdA2)
        assertThat(store.getNextPagesToIngest(installationId = installationBId, limit = 1)).isEmpty()

        // Mark another page as ingested, then check that the correct next page to be ingested is returned
        store.markAsIngested(installationId = installationAId, notionPageId = pageIdA2, lastEditedTime = Instant.nowWithMicrosecondPrecision())
        assertThat(store.getNextPagesToIngest(installationId = installationAId, limit = 1).single().notionPageId).isEqualTo(pageIdA1)
        assertThat(store.getNextPagesToIngest(installationId = installationBId, limit = 1)).isEmpty()

        // Finally, mark the last page as ingested, then check that we're done
        store.markAsIngested(installationId = installationAId, notionPageId = pageIdA1, lastEditedTime = Instant.nowWithMicrosecondPrecision())
        assertThat(store.getNextPagesToIngest(installationId = installationAId, limit = 1)).isEmpty()
        assertThat(store.getNextPagesToIngest(installationId = installationBId, limit = 1)).isEmpty()
    }

    @Test
    fun lastEditedTime() = suspendingDatabaseTest {
        val installationId = makeInstallation().idValue
        val notionPageId = UUID.randomUUID()

        assertThat(store.lastEditedTime(installationId = installationId, notionPageId = notionPageId)).isNull()

        store.markForIngestion(installationId = installationId, notionPageId = notionPageId, priority = null)
        assertThat(store.lastEditedTime(installationId = installationId, notionPageId = notionPageId)).isNull()

        val lastEditedTime = Instant.nowWithMicrosecondPrecision()

        store.markAsIngested(installationId = installationId, notionPageId = notionPageId, lastEditedTime = lastEditedTime)
        assertThat(store.lastEditedTime(installationId = installationId, notionPageId = notionPageId)).isEqualTo(lastEditedTime)
    }

    @Test
    fun `filterExists, markForDeletion, listPagesMarkedForDeletion`() = suspendingDatabaseTest {
        val installationAId = makeInstallation().idValue
        val installationBId = makeInstallation().idValue

        // Sanity check that there are no pages to ingest
        assertThat(store.getNextPagesToIngest(installationId = installationAId, limit = 1)).isEmpty()
        assertThat(store.getNextPagesToIngest(installationId = installationBId, limit = 1)).isEmpty()

        // Set up the pages, then check that the correct pages that need to be ingested are returned in the correct order
        store.markForIngestion(installationId = installationAId, notionPageId = pageIdA1, priority = null)
        store.markForIngestion(installationId = installationAId, notionPageId = pageIdA2, priority = null)
        store.markForIngestion(installationId = installationAId, notionPageId = pageIdA3, priority = null)
        store.markForIngestion(installationId = installationBId, notionPageId = pageIdB1, priority = null)
        store.markForIngestion(installationId = installationBId, notionPageId = pageIdB2, priority = null)
        store.markForIngestion(installationId = installationBId, notionPageId = pageIdB3, priority = null)

        val allNotionPages = setOf(pageIdA1, pageIdA2, pageIdA3, pageIdB1, pageIdB2, pageIdB3)

        assertThat(store.filterExists(installationIds = listOf(installationAId, installationBId), notionPageIds = allNotionPages))
            .containsExactlyInAnyOrderElementsOf(allNotionPages)
        assertThat(store.filterExists(installationIds = listOf(installationAId), notionPageIds = allNotionPages))
            .containsExactlyInAnyOrder(pageIdA1, pageIdA2, pageIdA3)
        assertThat(store.filterExists(installationIds = listOf(installationBId), notionPageIds = allNotionPages))
            .containsExactlyInAnyOrder(pageIdB1, pageIdB2, pageIdB3)
        assertThat(store.listPagesMarkedForDeletion()).isEmpty()

        store.markForDeletion(installationId = installationAId, notionPageIds = setOf(pageIdA1))

        assertThat(store.filterExists(installationIds = listOf(installationAId, installationBId), notionPageIds = allNotionPages))
            .containsExactlyInAnyOrder(pageIdA2, pageIdA3, pageIdB1, pageIdB2, pageIdB3)
        assertThat(store.filterExists(installationIds = listOf(installationAId), notionPageIds = allNotionPages))
            .containsExactlyInAnyOrder(pageIdA2, pageIdA3)
        assertThat(store.filterExists(installationIds = listOf(installationBId), notionPageIds = allNotionPages))
            .containsExactlyInAnyOrder(pageIdB1, pageIdB2, pageIdB3)
        assertThat(store.listPagesMarkedForDeletion().map { it.notionPageId }).containsExactlyInAnyOrder(pageIdA1)

        // Undo
        store.markForIngestion(installationId = installationAId, notionPageId = pageIdA1, priority = null)

        assertThat(store.filterExists(installationIds = listOf(installationAId, installationBId), notionPageIds = allNotionPages))
            .containsExactlyInAnyOrderElementsOf(allNotionPages)
        assertThat(store.filterExists(installationIds = listOf(installationAId), notionPageIds = allNotionPages))
            .containsExactlyInAnyOrder(pageIdA1, pageIdA2, pageIdA3)
        assertThat(store.filterExists(installationIds = listOf(installationBId), notionPageIds = allNotionPages))
            .containsExactlyInAnyOrder(pageIdB1, pageIdB2, pageIdB3)
        assertThat(store.listPagesMarkedForDeletion()).isEmpty()
    }

    @Test
    fun exists() = suspendingDatabaseTest {
        val org = makeOrg()
        val installationId = makeInstallation(org = org).idValue
        val notionPageId = UUID.randomUUID()

        assertThat(store.exists(installationId = installationId, notionPageId = notionPageId)).isFalse()
        store.markForIngestion(installationId = installationId, notionPageId = notionPageId, priority = null)
        assertThat(store.exists(installationId = installationId, notionPageId = notionPageId)).isTrue()
    }

    @Test
    fun setStatusToNullForAllErroredPages() = suspendingDatabaseTest {
        val org = makeOrg()
        val installation = makeInstallation(org = org).idValue
        val notionPageId1 = UUID.randomUUID()
        val notionPageId2 = UUID.randomUUID()

        store.markForIngestion(installationId = installation, notionPageId = notionPageId1, priority = null)
        store.markForIngestion(installationId = installation, notionPageId = notionPageId2, priority = null)

        assertThat(store.getNextPagesToIngest(installationId = installation, limit = 2).map { it.notionPageId })
            .containsExactlyInAnyOrder(notionPageId1, notionPageId2)

        store.markAsErrored(installationId = installation, notionPageId = notionPageId1)

        assertThat(store.getNextPagesToIngest(installationId = installation, limit = 2).map { it.notionPageId })
            .containsExactlyInAnyOrder(notionPageId2)

        store.markAsIngested(installationId = installation, notionPageId = notionPageId2, lastEditedTime = Instant.nowWithMicrosecondPrecision())

        assertThat(store.getNextPagesToIngest(installationId = installation, limit = 2).map { it.notionPageId })
            .isEmpty()

        store.setStatusToNullForAllErroredPages()

        assertThat(store.getNextPagesToIngest(installationId = installation, limit = 2).map { it.notionPageId })
            .containsExactlyInAnyOrder(notionPageId1)
    }

    @Test
    fun listNotionPagesNotAccessibleToIdentity() = suspendingDatabaseTest {
        val identityA = makeIdentity()
        val identityB = makeIdentity()
        val identityC = makeIdentity()

        val installationA = makeInstallation()
        val installationB = makeInstallation()

        val notionPageId1 = UUID.randomUUID()
        val notionPageId2 = UUID.randomUUID()
        val notionPageId3 = UUID.randomUUID()

        store.markForIngestion(installationId = installationA.idValue, notionPageId = notionPageId1, priority = null)
        store.markForIngestion(installationId = installationA.idValue, notionPageId = notionPageId2, priority = null)
        store.markForIngestion(installationId = installationB.idValue, notionPageId = notionPageId3, priority = null)

        Stores.notionPageAccessStore.upsert(identityId = identityA.idValue, notionPageId = notionPageId1)
        Stores.notionPageAccessStore.upsert(identityId = identityB.idValue, notionPageId = notionPageId2)
        Stores.notionPageAccessStore.upsert(identityId = identityC.idValue, notionPageId = notionPageId3)

        assertThat(
            store.listNotionPagesNotAccessibleToIdentity(installationId = installationA.idValue, identityId = identityA.idValue)
                .map { it.notionPageId },
        ).containsExactly(notionPageId2)

        assertThat(
            store.listNotionPagesNotAccessibleToIdentity(installationId = installationA.idValue, identityId = identityB.idValue)
                .map { it.notionPageId },
        ).containsExactly(notionPageId1)

        assertThat(
            store.listNotionPagesNotAccessibleToIdentity(installationId = installationB.idValue, identityId = identityC.idValue),
        ).isEmpty()

        Stores.notionPageAccessStore.upsert(identityId = identityA.idValue, notionPageId = notionPageId2)

        assertThat(
            store.listNotionPagesNotAccessibleToIdentity(installationId = installationA.idValue, identityId = identityA.idValue),
        ).isEmpty()
    }
}
