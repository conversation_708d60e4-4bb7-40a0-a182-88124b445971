package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption.CASCADE
import org.jetbrains.exposed.sql.ResultRow

object PersonPreferencesModel : ServiceModel<PersonPreferencesId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val person = reference(
        name = "person",
        foreign = PersonModel,
        onDelete = CASCADE,
        onUpdate = CASCADE,
    )

    val enableCiTriage = bool("enableCiTriage").nullable()
    val incognitoMode = bool("incognitoMode").nullable()
    val hideIncognitoBanner = bool("hideIncognitoBanner").nullable()

    init {
        uniqueIndex(person)
        index(isUnique = false, incognitoMode)
    }
}

fun ResultRow.toPersonPreferences(alias: String? = null) = PersonPreferencesDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toPersonPreferencesOrNull(alias: String? = null) = PersonPreferencesDAO.wrapRowOrNull(this, alias)?.asDataModel()

class PersonPreferencesDAO(id: EntityID<PersonPreferencesId>) : EntityExtensions<PersonPreferences, PersonPreferencesId>(id) {
    companion object : EntityClassExtensions<PersonPreferencesId, PersonPreferencesDAO>(PersonPreferencesModel)

    override var createdAt by PersonPreferencesModel.createdAt
    override var modifiedAt by PersonPreferencesModel.modifiedAt

    var person by PersonDAO referencedOn PersonPreferencesModel.person

    var enableCiTriage by PersonPreferencesModel.enableCiTriage
    var incognitoMode by PersonPreferencesModel.incognitoMode
    var hideIncognitoBanner by PersonPreferencesModel.hideIncognitoBanner

    override fun asDataModel() = readValues.let {
        PersonPreferences(
            id = it[PersonPreferencesModel.id].value,
            createdAt = it[PersonPreferencesModel.createdAt],
            modifiedAt = it[PersonPreferencesModel.modifiedAt],
            personId = it[PersonPreferencesModel.person].value,
            enableCiTriage = it[PersonPreferencesModel.enableCiTriage] ?: true,
            incognitoMode = it[PersonPreferencesModel.incognitoMode],
            hideIncognitoBanner = it[PersonPreferencesModel.hideIncognitoBanner],
        )
    }
}

object PersonPreferencesIdConverter : ValueIdConverter<PersonPreferencesId> {
    override val factory = ::PersonPreferencesId
    override val extract = PersonPreferencesId::value
}

internal object PersonPreferencesIdSerializer : ValueIdSerializer<PersonPreferencesId>(
    serialName = "PersonPreferencesId",
    converter = PersonPreferencesIdConverter,
)

@Serializable(with = PersonPreferencesIdSerializer::class)
data class PersonPreferencesId(val value: UUID) : ValueId {

    companion object : ValueIdClass<PersonPreferencesId>(
        converter = PersonPreferencesIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ValueId): Int {
        check(other is PersonPreferencesId)
        return value.compareTo(other.value)
    }
}

data class PersonPreferences(
    val id: PersonPreferencesId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val personId: PersonId,
    val enableCiTriage: Boolean,
    val incognitoMode: Boolean?,
    val hideIncognitoBanner: Boolean?,
)
