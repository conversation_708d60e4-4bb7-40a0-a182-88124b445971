package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.common.withBitFieldPositionsSet
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.IdentityModel
import com.nextchaptersoftware.db.models.InstallationModel
import com.nextchaptersoftware.db.models.MemberModel
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.OrgMemberModel
import com.nextchaptersoftware.db.models.Person
import com.nextchaptersoftware.db.models.PersonDAO
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.PersonModel
import com.nextchaptersoftware.db.models.PersonOnboardingState
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ReleaseChannel
import com.nextchaptersoftware.db.models.toIdentity
import com.nextchaptersoftware.db.models.toPerson
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.log.kotlin.errorSync
import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.types.EmailAddress
import com.nextchaptersoftware.utils.KotlinUtils.required
import kotlinx.datetime.Instant
import mu.KotlinLogging
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.LikePattern
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.Query
import org.jetbrains.exposed.sql.SqlExpressionBuilder.bitwiseAnd
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.SqlExpressionBuilder.less
import org.jetbrains.exposed.sql.SqlExpressionBuilder.neq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.notInList
import org.jetbrains.exposed.sql.Transaction
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.deleteWhere
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.update

private val LOGGER = KotlinLogging.logger {}

class PersonStore internal constructor() {

    suspend fun createPerson(
        trx: Transaction? = null,
        identity: Identity,
    ): Person {
        return suspendedTransaction(trx) {
            PersonModel.insert { row ->
                row[primaryEmail] = identity.primaryEmail.required().value
                row[customDisplayName] = identity.displayName ?: identity.username
                row[customAvatarUrl] = identity.avatarUrl.asString
            }.resultedValues.required().map { it.toPerson() }.first()
        }
    }

    suspend fun allIds(trx: Transaction? = null) = suspendedTransaction(trx = trx) {
        PersonModel
            .select(PersonModel.id)
            .map { it[PersonModel.id].value }
    }

    suspend fun findByEmail(
        trx: Transaction? = null,
        primaryEmail: EmailAddress,
    ): Person? {
        fun findByPersonEmail(): Person? {
            return PersonModel
                .selectAll()
                .where { PersonModel.primaryEmail eq primaryEmail.value }
                .map { it.toPerson() }
                .firstOrNull()
        }

        fun findByIdentityEmail(): Person? {
            // Email MUST be enclosed in double quotes to match exactly within a JSON encoded email list
            val emailPattern = LikePattern("""%"${primaryEmail.value}"%""")

            return IdentityModel
                .join(otherTable = PersonModel, joinType = JoinType.INNER, onColumn = IdentityModel.person, otherColumn = PersonModel.id)
                .select(PersonModel.columns)
                .where { IdentityModel.emails like emailPattern }
                .map { it.toPerson() }
                .distinct()
                .also {
                    if (it.count() > 1) {
                        val personIds = it.joinToString { person -> person.id.toString() }
                        LOGGER.errorSync("personIds" to personIds) {
                            "More than one person model matched on email"
                        }
                    }
                }
                .firstOrNull()
        }

        return suspendedTransaction(trx) {
            findByPersonEmail() ?: findByIdentityEmail()
        }
    }

    suspend fun findById(
        id: PersonId,
        trx: Transaction? = null,
    ): Person? {
        return suspendedTransaction(trx = trx) {
            PersonDAO.findById(id)?.asDataModel()
        }
    }

    suspend fun findByIdentityId(
        identityId: IdentityId,
    ): Person? {
        return suspendedTransaction {
            IdentityModel
                .join(otherTable = PersonModel, joinType = JoinType.INNER, onColumn = IdentityModel.person, otherColumn = PersonModel.id)
                .select(PersonModel.columns)
                .where { IdentityModel.id eq identityId }
                .limit(1)
                .firstOrNull()
                ?.toPerson()
        }
    }

    private fun findByOrgMemberQuery(
        orgId: OrgId,
        orgMemberIds: Collection<OrgMemberId>,
    ): Query = OrgMemberModel
        .join(
            otherTable = PersonModel,
            otherColumn = PersonModel.id,
            onColumn = OrgMemberModel.person,
            joinType = JoinType.INNER,
        )
        .select(
            PersonModel.columns + OrgMemberModel.id,
        )
        .whereAll(
            OrgMemberModel.org eq orgId,
            OrgMemberModel.id inList orgMemberIds.distinct(),
        )

    /**
     * Returns the Persons associated with the given [orgMemberId], or `null` if it does not exist.
     */
    suspend fun findByOrgMemberId(
        trx: Transaction? = null,
        orgId: OrgId,
        orgMemberId: OrgMemberId,
    ): Person? = suspendedTransaction(trx) {
        findByOrgMemberQuery(
            orgId = orgId,
            orgMemberIds = listOf(orgMemberId),
        )
            .limit(1)
            .firstOrNull()
            ?.toPerson()
    }

    /**
     * Returns the Persons associated for all the given [orgMemberIds].
     */
    suspend fun findByOrgMemberIds(
        trx: Transaction? = null,
        orgId: OrgId,
        orgMemberIds: Collection<OrgMemberId>,
    ): Map<OrgMemberId, Person> {
        if (orgMemberIds.isEmpty()) {
            return emptyMap()
        }
        return suspendedTransaction(trx) {
            findByOrgMemberQuery(
                orgId = orgId,
                orgMemberIds = orgMemberIds,
            ).associate {
                val orgMemberId = it[OrgMemberModel.id].value
                val person = it.toPerson()
                orgMemberId to person
            }
        }
    }

    suspend fun setHasSeenTutorial(
        trx: Transaction? = null,
        id: PersonId,
        value: Boolean,
        agent: ProductAgentType,
    ): Boolean = setOnboardingFlags(
        trx = trx,
        id = id,
        flags = mapOf(
            when (agent) {
                ProductAgentType.VSCode -> PersonOnboardingState.VSCodeHasSeenTutorial

                ProductAgentType.IntelliJ -> PersonOnboardingState.IntellijHasSeenTutorial

                ProductAgentType.Desktop,
                ProductAgentType.Hub,
                    -> PersonOnboardingState.HubHasSeenTutorial

                ProductAgentType.Dashboard,
                ProductAgentType.SafariExtension,
                ProductAgentType.ChromeExtension,
                ProductAgentType.IOS,
                ProductAgentType.Mcp,
                ProductAgentType.Unknown,
                ProductAgentType.Slack,
                ProductAgentType.Landing,
                    -> throw IllegalArgumentException("Invalid agent type")
            } to value,
        ),
    )

    suspend fun setHasSeenTopFile(
        trx: Transaction? = null,
        id: PersonId,
        value: Boolean,
        agent: ProductAgentType,
    ): Boolean = setOnboardingFlags(
        trx = trx,
        id = id,
        flags = mapOf(
            when (agent) {
                ProductAgentType.VSCode -> PersonOnboardingState.VSCodeHasSeenTopFile

                ProductAgentType.IntelliJ -> PersonOnboardingState.IntellijHasSeenTopFile

                ProductAgentType.Dashboard,
                ProductAgentType.SafariExtension,
                ProductAgentType.ChromeExtension,
                ProductAgentType.Hub,
                ProductAgentType.IOS,
                ProductAgentType.Mcp,
                ProductAgentType.Unknown,
                ProductAgentType.Slack,
                ProductAgentType.Landing,
                ProductAgentType.Desktop,
                    -> throw IllegalArgumentException("Invalid agent type")
            } to value,
        ),
    )

    suspend fun setHasCreatedNote(
        trx: Transaction? = null,
        id: PersonId,
        value: Boolean,
        agent: ProductAgentType,
    ): Boolean = setOnboardingFlags(
        trx = trx,
        id = id,
        flags = mapOf(
            when (agent) {
                ProductAgentType.VSCode -> PersonOnboardingState.VSCodeHasCreatedNote

                ProductAgentType.IntelliJ -> PersonOnboardingState.IntellijHasCreatedNote

                ProductAgentType.Dashboard,
                ProductAgentType.SafariExtension,
                ProductAgentType.ChromeExtension,
                ProductAgentType.Hub,
                ProductAgentType.IOS,
                ProductAgentType.Mcp,
                ProductAgentType.Unknown,
                ProductAgentType.Slack,
                ProductAgentType.Landing,
                ProductAgentType.Desktop,
                    -> throw IllegalArgumentException("Invalid agent type")
            } to value,
        ),
    )

    suspend fun setHasInstalledHub(
        trx: Transaction? = null,
        id: PersonId,
        value: Boolean,
    ): Boolean = setOnboardingFlags(
        trx = trx,
        id = id,
        flags = mapOf(
            PersonOnboardingState.HubIsInstalled to value,
        ),
    )

    suspend fun setHasInstalledDesktop(
        trx: Transaction? = null,
        id: PersonId,
        value: Boolean,
    ): Boolean = setOnboardingFlags(
        trx = trx,
        id = id,
        flags = mapOf(
            PersonOnboardingState.DesktopIsInstalled to value,
        ),
    )

    suspend fun setHasSeenDocs(
        trx: Transaction? = null,
        id: PersonId,
        value: Boolean,
    ): Boolean = setOnboardingFlags(
        trx = trx,
        id = id,
        flags = mapOf(
            PersonOnboardingState.HasSeenDocs to value,
        ),
    )

    suspend fun setHasInstalledIDEPlugins(
        trx: Transaction? = null,
        id: PersonId,
        value: Boolean,
    ): Boolean = setOnboardingFlags(
        trx = trx,
        id = id,
        flags = mapOf(
            PersonOnboardingState.IDEPluginsInstalled to value,
        ),
    )

    suspend fun setSentOnboardingEmailCampaign(
        trx: Transaction? = null,
        id: PersonId,
        value: Boolean,
    ): Boolean = setOnboardingFlags(
        trx = trx,
        id = id,
        flags = mapOf(
            PersonOnboardingState.SentOnboardingEmailCampaign to value,
        ),
    )

    suspend fun setHasSeenAnswersTutorial(
        trx: Transaction? = null,
        id: PersonId,
        value: Boolean,
    ): Boolean = setOnboardingFlags(
        trx = trx,
        id = id,
        flags = mapOf(
            PersonOnboardingState.HasSeenAnswersTutorial to value,
        ),
    )

    suspend fun setHasDismissedToast(
        trx: Transaction? = null,
        id: PersonId,
        value: Boolean,
        agent: ProductAgentType,
    ): Boolean = setOnboardingFlags(
        trx = trx,
        id = id,
        flags = mapOf(
            when (agent) {
                ProductAgentType.VSCode -> PersonOnboardingState.VSCodeHasDismissedToast

                ProductAgentType.IntelliJ -> PersonOnboardingState.IntellijHasDismissedToast

                ProductAgentType.Dashboard,
                ProductAgentType.SafariExtension,
                ProductAgentType.ChromeExtension,
                ProductAgentType.Hub,
                ProductAgentType.IOS,
                ProductAgentType.Mcp,
                ProductAgentType.Unknown,
                ProductAgentType.Slack,
                ProductAgentType.Landing,
                ProductAgentType.Desktop,
                    -> throw IllegalArgumentException("Invalid agent type")
            } to value,
        ),
    )

    suspend fun resetVSCodeOnboardingStates(
        trx: Transaction? = null,
        id: PersonId,
    ): Boolean = setOnboardingFlags(
        trx = trx,
        id = id,
        flags = mapOf(
            PersonOnboardingState.VSCodeHasSeenTutorial to false,
            PersonOnboardingState.VSCodeHasSeenTopFile to false,
            PersonOnboardingState.VSCodeHasCreatedNote to false,
            PersonOnboardingState.VSCodeHasDismissedToast to false,
        ),
    )

    suspend fun resetIntellijOnboardingStates(
        trx: Transaction? = null,
        id: PersonId,
    ): Boolean = setOnboardingFlags(
        trx = trx,
        id = id,
        flags = mapOf(
            PersonOnboardingState.IntellijHasSeenTutorial to false,
            PersonOnboardingState.IntellijHasSeenTopFile to false,
            PersonOnboardingState.IntellijHasCreatedNote to false,
            PersonOnboardingState.IntellijHasCreatedWalkthrough to false,
            PersonOnboardingState.IntellijHasDismissedToast to false,
        ),
    )

    suspend fun resetHubOnboardingStates(
        trx: Transaction? = null,
        id: PersonId,
    ): Boolean = setOnboardingFlags(
        trx = trx,
        id = id,
        flags = mapOf(
            PersonOnboardingState.HubHasSeenTutorial to false,
            PersonOnboardingState.HubIsInstalled to false,
        ),
    )

    suspend fun setOnboardingFlags(
        trx: Transaction? = null,
        id: PersonId,
        flags: Map<PersonOnboardingState, Boolean>,
    ): Boolean = suspendedTransaction(trx) {
        val person = PersonDAO.findById(id) ?: return@suspendedTransaction false
        val currentStates = person.onboardingStates ?: 0u

        return@suspendedTransaction when (val updatedStates = currentStates.withBitFieldPositionsSet(positionAndValues = flags)) {
            currentStates -> {
                false
            }

            else -> {
                person.onboardingStates = updatedStates
                true
            }
        }
    }

    suspend fun countPeople(
        createdBefore: Instant,
        excludeIds: List<PersonId> = emptyList(),
    ): Long {
        return suspendedTransaction {
            PersonDAO.count(
                AllOp(
                    PersonModel.createdAt less createdBefore,
                    PersonModel.id notInList excludeIds,
                ),
            )
        }
    }

    /**
     * Get trusted emails for a person.
     * Excludes emails that contain "no reply" or "do not reply" in various forms.
     */
    suspend fun getTrustedPersonEmails(personId: PersonId): Set<EmailAddress> {
        return suspendedTransaction {
            PersonModel
                .join(
                    joinType = JoinType.INNER,
                    otherTable = IdentityModel,
                    otherColumn = IdentityModel.person,
                    onColumn = PersonModel.id,
                )
                .select(PersonModel.primaryEmail, IdentityModel.emails)
                .where { PersonModel.id eq personId }
                .map { it[IdentityModel.emails]?.decode<List<String>>().orEmpty() + it[PersonModel.primaryEmail] }
                .flatten()
                .distinct()
                .filterNot { it.isBlank() }
                .filterNot { it.contains(Regex("no.?reply", RegexOption.IGNORE_CASE)) }
                .filterNot { it.contains(Regex("do.not.reply", RegexOption.IGNORE_CASE)) }
                .map { EmailAddress.of(it) }
                .toSet()
        }
    }

    suspend fun updatePrimaryEmail(id: PersonId, email: EmailAddress) {
        suspendedTransaction {
            PersonModel.update({ PersonModel.id eq id }) {
                it[primaryEmail] = email.value
            }
        }
    }

    suspend fun updateDisplayName(id: PersonId, displayName: String) {
        suspendedTransaction {
            PersonModel.update({ PersonModel.id eq id }) {
                it[customDisplayName] = displayName
            }
        }
    }

    suspend fun deletePerson(id: PersonId) {
        suspendedTransaction {
            PersonModel.deleteWhere { PersonModel.id eq id }
        }
    }

    /**
     * WARNING: This is a slow query if you do not provide a where clause to filter down the search.
     */
    suspend fun findByOnboardingState(
        onboardingState: PersonOnboardingState,
        whereClause: Op<Boolean>? = null,
    ): List<Person> {
        return suspendedTransaction {
            PersonModel
                .selectAll()
                .whereAll(
                    (PersonModel.onboardingStates bitwiseAnd onboardingState.asBitPosition()) neq 0u,
                    whereClause,
                ).map {
                    it.toPerson()
                }
        }
    }

    suspend fun findByExcludingOnboardingState(
        excludeOnboardingState: PersonOnboardingState,
        whereClause: Op<Boolean>? = null,
    ): List<Person> = suspendedTransaction {
        PersonModel
            .selectAll()
            .whereAll(
                (PersonModel.onboardingStates bitwiseAnd excludeOnboardingState.asBitPosition()) eq 0u,
                whereClause,
            ).map {
                it.toPerson()
            }
    }

    suspend fun getSubscribedReleaseChannel(
        trx: Transaction? = null,
        id: PersonId,
    ): ReleaseChannel? = suspendedTransaction(trx) {
        PersonModel
            .selectAll()
            .where {
                PersonModel.id eq id
            }.firstOrNull()?.let {
                it[PersonModel.subscribedReleaseChannel]
            }
    }

    suspend fun setSubscribedReleaseChannel(
        trx: Transaction? = null,
        id: PersonId,
        releaseChannel: ReleaseChannel,
    ) = suspendedTransaction(trx) {
        PersonModel.update({ PersonModel.id eq id }) {
            it[subscribedReleaseChannel] = releaseChannel
        }
    }

    suspend fun findAllWithReleaseChannelOverrides(): List<Person> = suspendedTransaction {
        PersonModel
            .selectAll()
            .where {
                PersonModel.subscribedReleaseChannel neq null
            }.map {
                it.toPerson()
            }
    }

    suspend fun createdAccountAts(ids: List<PersonId>): Map<PersonId, Instant> = suspendedTransaction {
        ids.chunked(MAX_CHUNK_SIZE).flatMap { chunk ->
            PersonModel
                .select(PersonModel.id, PersonModel.createdAt)
                .where { PersonModel.id inList chunk }
                .map { it[PersonModel.id].value to it[PersonModel.createdAt] }
        }.toMap()
    }

    suspend fun getOrgsForPeople(
        personIds: List<PersonId>,
        providers: Set<Provider> = Provider.scmProviders,
    ): Map<PersonId, List<OrgId>> {
        return suspendedTransaction {
            MemberModel
                .join(otherTable = IdentityModel, otherColumn = IdentityModel.id, onColumn = MemberModel.identity, joinType = JoinType.INNER) {
                    IdentityModel.provider inList providers
                }
                .join(
                    joinType = JoinType.INNER,
                    otherTable = PersonModel,
                    onColumn = IdentityModel.person,
                    otherColumn = PersonModel.id,
                ) {
                    PersonModel.id inList personIds
                }
                .join(
                    joinType = JoinType.INNER,
                    otherTable = InstallationModel,
                    otherColumn = InstallationModel.id,
                    onColumn = MemberModel.installation,
                ) {
                    InstallationStore.INSTALLATION_EXISTS
                }
                .select(PersonModel.id, InstallationModel.org)
                .where { MemberStore.IS_PRIMARY_CURRENT_MEMBER }
                .withDistinct(true)
                .groupBy({ it[PersonModel.id].value }) { it[InstallationModel.org].value }
        }
    }

    /**
     * Lookup all the person's identities
     */
    suspend fun getIdentitiesForPerson(
        trx: Transaction? = null,
        personId: PersonId,
        providers: Collection<Provider> = Provider.scmProviders,
    ): List<Identity> {
        return suspendedTransaction(trx = trx) {
            IdentityModel
                .selectAll()
                .where {
                    (IdentityModel.person eq personId) and (IdentityModel.provider inList providers)
                }
                .map { it.toIdentity() }
        }
    }

    /**
     * Lookup all the peoples identity providers
     */
    suspend fun getIdentityProvidersForPeople(
        trx: Transaction? = null,
        personIds: List<PersonId>,
        providers: Set<Provider> = Provider.scmProviders,
    ): Map<PersonId, List<Provider>> {
        return suspendedTransaction(trx = trx) {
            IdentityModel
                .select(IdentityModel.person, IdentityModel.provider)
                .where {
                    (IdentityModel.person inList personIds) and (IdentityModel.provider inList providers)
                }
                .groupBy({ checkNotNull(it[IdentityModel.person]).value }) { it[IdentityModel.provider] }
        }
    }

    suspend fun mergePeople(
        trx: Transaction? = null,
        fromPerson: PersonId,
        toPerson: PersonId,
    ): PersonId {
        if (fromPerson == toPerson) {
            return toPerson
        }

        LOGGER.infoAsync(
            "fromPerson" to fromPerson,
            "toPerson" to toPerson,
        ) { "Merging people" }

        suspendedTransaction(trx) {
            IdentityModel.update({ IdentityModel.person eq fromPerson }) {
                it[this.person] = toPerson
            }
            PersonModel.deleteWhere { PersonModel.id eq fromPerson }
        }

        return toPerson
    }

    suspend fun findCreatedBetween(
        from: Instant,
        to: Instant,
    ): List<Person> {
        return suspendedTransaction {
            PersonDAO
                .find { PersonModel.createdAt greater from and (PersonModel.createdAt less to) }
                .map { it.asDataModel() }
        }
    }
}

private const val MAX_CHUNK_SIZE = 5000 // Arbitrary limit to prevent large IN clauses
