package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.CIRepoSelectionMode
import com.nextchaptersoftware.db.models.CIScm
import com.nextchaptersoftware.db.models.CIScmModel
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.toCIScm
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.Transaction
import org.jetbrains.exposed.sql.count
import org.jetbrains.exposed.sql.deleteWhere
import org.jetbrains.exposed.sql.insertReturning
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.upsert

class CIScmStore internal constructor() {

    suspend fun getOrCreateCIScm(
        trx: Transaction? = null,
        ciInstallationId: InstallationId,
        scmInstallationId: InstallationId,
    ): CIScm {
        return suspendedTransaction(trx) {
            getCIScm(
                trx = this,
                ciInstallationId = ciInstallationId,
                scmInstallationId = scmInstallationId,
            ) ?: createCIScm(
                trx = this,
                ciInstallationId = ciInstallationId,
                scmInstallationId = scmInstallationId,
            )
        }
    }

    fun upsertCIScm(
        trx: Transaction,
        ciInstallationId: InstallationId,
        scmInstallationId: InstallationId,
        mode: CIRepoSelectionMode,
    ) {
        return trx.run {
            CIScmModel.upsert(
                keys = arrayOf(
                    CIScmModel.ciInstallation,
                    CIScmModel.scmInstallation,
                ),
                onUpdateExclude = CIScmModel.columns - setOfNotNull(
                    CIScmModel.mode,
                ),
            ) { stmt ->
                stmt[this.ciInstallation] = ciInstallationId
                stmt[this.scmInstallation] = scmInstallationId
                stmt[this.mode] = mode
            }
        }
    }

    suspend fun deleteAllCIScms(ciInstallationId: InstallationId) {
        suspendedTransaction {
            CIScmModel.deleteWhere {
                this.ciInstallation eq ciInstallationId
            }
        }
    }

    suspend fun hasAnyAllModeScms(ciInstallationId: InstallationId): Boolean {
        return suspendedTransaction {
            CIScmModel
                .select(CIScmModel.id.count())
                .whereAll(
                    CIScmModel.ciInstallation eq ciInstallationId,
                    CIScmModel.mode eq CIRepoSelectionMode.All,
                )
                .limit(1)
                .first()[CIScmModel.id.count()] > 0
        }
    }

    private fun getCIScm(
        trx: Transaction,
        ciInstallationId: InstallationId,
        scmInstallationId: InstallationId,
    ): CIScm? {
        return trx.run {
            CIScmModel
                .selectAll()
                .whereAll(
                    CIScmModel.ciInstallation eq ciInstallationId,
                    CIScmModel.scmInstallation eq scmInstallationId,
                )
                .map { it.toCIScm() }
                .firstOrNull()
        }
    }

    private fun createCIScm(
        trx: Transaction,
        ciInstallationId: InstallationId,
        scmInstallationId: InstallationId,
    ): CIScm {
        return trx.run {
            CIScmModel.insertReturning {
                it[this.ciInstallation] = ciInstallationId
                it[this.scmInstallation] = scmInstallationId
                it[this.mode] = CIRepoSelectionMode.None
            }.first().toCIScm()
        }
    }
}
