package com.nextchaptersoftware.asana.api

import com.nextchaptersoftware.asana.api.AsanaHttpClient.setAuthorizationHeader
import com.nextchaptersoftware.asana.api.models.Project
import com.nextchaptersoftware.asana.api.models.Results
import com.nextchaptersoftware.asana.api.models.Single
import com.nextchaptersoftware.auth.oauth.OAuthTokens
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.ktor.client.HttpClientBatch
import com.nextchaptersoftware.ktor.client.HttpClientPagination.batchStream
import com.nextchaptersoftware.pagination.asFlatItemsFlow
import com.nextchaptersoftware.utils.FlowExtensions.suspendedFlow
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.http.URLBuilder
import io.ktor.http.Url
import io.ktor.http.path
import kotlinx.coroutines.flow.Flow

class AsanaProjectsApi(
    private val tokenProvider: AsanaOauthTokenProvider,
    private val client: HttpClient,
    private val baseUrl: Url,
) {
    companion object {
        private const val RESOURCE_PATH = "projects"
        private const val PARENT_RESOURCE_PATH = "workspaces"
        private const val GET_OPT_FIELDS = "modified_at,name,notes,members,followers,projects,parent"
        private const val STREAM_OPT_FIELDS = "modified_at,name,notes,members,followers"
    }

    suspend fun find(
        tokens: OAuthTokens,
        workspaceId: String? = null,
        limit: Int = 100,
        offset: String? = null,
    ): Results<Project> {
        val url = URLBuilder(baseUrl).apply {
            when (workspaceId) {
                null -> path(*baseUrl.segments.toTypedArray(), RESOURCE_PATH)
                else -> path(*baseUrl.segments.toTypedArray(), PARENT_RESOURCE_PATH, workspaceId, RESOURCE_PATH)
            }
            parameters["limit"] = limit.toString()
            offset?.let { parameters["offset"] = it }
        }.build()

        return client.get(url) {
            setAuthorizationHeader(accessToken = tokens.accessToken)
        }.body()
    }

    suspend fun get(resourceId: String, tokens: OAuthTokens): Project {
        val url = URLBuilder(baseUrl).apply {
            path(*baseUrl.segments.toTypedArray(), RESOURCE_PATH, resourceId)
            parameters["opt_fields"] = GET_OPT_FIELDS
        }.build()

        val wrapper: Single<Project> = client.get(url) {
            setAuthorizationHeader(accessToken = tokens.accessToken)
        }.body()

        return wrapper.data
    }

    suspend fun get(resourceId: String, installationId: InstallationId): Project = get(
        resourceId,
        tokens = tokenProvider.getOAuthTokens(
        installationId = installationId,
    ),
    )

    private fun batchStream(
        tokens: OAuthTokens,
        workspaceId: String,
        maxItems: Int? = null,
    ): Flow<HttpClientBatch<Project>> {
        val initialUri = "$baseUrl$PARENT_RESOURCE_PATH/$workspaceId/$RESOURCE_PATH?opt_fields=$STREAM_OPT_FIELDS"

        return client.batchStream(
            initialUri = initialUri,
            maxItems = maxItems,
            dataProvider = { response ->
                response.body<Results<Project>>().data
            },
            nextUriProvider = { response ->
                response.body<Results<Project>>().nextPage?.uri?.let { Url(it) }
            },
            block = {
                setAuthorizationHeader(tokens.accessToken)
            },
        )
    }

    fun stream(
        tokens: OAuthTokens,
        workspaceId: String,
        maxItems: Int? = null,
    ): Flow<Project> = batchStream(tokens, workspaceId, maxItems).asFlatItemsFlow()

    fun stream(installationId: InstallationId, workspaceId: String): Flow<Project> = suspendedFlow {
        stream(
            tokens = tokenProvider.getOAuthTokens(installationId = installationId),
            workspaceId = workspaceId,
        )
    }
}
