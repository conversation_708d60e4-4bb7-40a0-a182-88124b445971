package com.nextchaptersoftware.scm.github.models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * https://docs.github.com/en/webhooks/webhook-events-and-payloads#discussion
 */
@Suppress("ConstructorParameterNaming")
@Serializable
data class GitHubDiscussionEvent(
    @SerialName("action") val _action: String,
    val repository: GitHubRepo,
    val discussion: GitHubDiscussion,
) {
    val action: Action
        get() = Action.entries.firstOrNull { it.str.lowercase() == _action.lowercase() } ?: Action.Unknown

    enum class Action(val str: String) {
        Answered("Answered"),
        CategoryChanged("Category_Changed"),
        Closed("Closed"),
        Created("Created"),
        Deleted("Deleted"),
        Edited("Edited"),
        Labeled("Labeled"),
        Locked("Locked"),
        Pinned("Pinned"),
        Reopened("Reopened"),
        Transferred("Transferred"),
        Unanswered("Unanswered"),
        Unlabeled("Unlabeled"),
        Unlocked("Unlocked"),
        Unpinned("Unpinned"),
        Unknown("Unknown"), // catch-all for any new actions GitHub might add
    }

    val ownerExternalId: String
        get() = repository.owner.id.toString()

    val repoExternalId: String
        get() = repository.id.toString()
}
