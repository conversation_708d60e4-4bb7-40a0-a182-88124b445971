package com.nextchaptersoftware.linear.api

import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.linear.api.utils.LinearTestUtils
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
class LinearProjectsApiTest {
    @Test
    fun `test projects query`() = runTest {
        val projects = LinearTestUtils.LINEAR_API_PROVIDER.linearProjectsApi.getProjects(
            installationId = InstallationId.random(),
            linearTeamId = "97968c78-47e1-432a-bed3-062441b16406",
        )
        assertThat(projects.data?.team?.projects?.nodes).isNotEmpty
    }
}
