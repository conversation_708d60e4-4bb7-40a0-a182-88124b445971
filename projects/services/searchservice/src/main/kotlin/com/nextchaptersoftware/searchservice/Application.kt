package com.nextchaptersoftware.searchservice

import com.nextchaptersoftware.db.health.PostgresHealthChecker
import com.nextchaptersoftware.service.bootstrap.ServiceBootstrapBuilder

fun main() {
    ServiceBootstrapBuilder.bootstrap {
        withConditionalCoroutineDebugProbes()
        withHealthCheckers(listOf(PostgresHealthChecker()))
        withLogs()
        withHoneycomb()
        withDatabase()
        withShutdownHook()
    }.startHttpServer { serviceLifecycle ->
        module(
            serviceLifecycle = serviceLifecycle,
        )
    }
}
