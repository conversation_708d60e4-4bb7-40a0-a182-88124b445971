package com.nextchaptersoftware.ciservice.handlers

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.ci.payloads.CiEvent
import com.nextchaptersoftware.ciservice.handlers.buildkite.BuildkiteBuildEventHandler
import com.nextchaptersoftware.ciservice.handlers.buildkite.BuildkiteJobEventHandler
import com.nextchaptersoftware.ciservice.handlers.builds.BuildEventHandler
import com.nextchaptersoftware.ciservice.handlers.builds.BuildJobEventHandler
import com.nextchaptersoftware.ciservice.handlers.builds.BuildTriageTaskHandler
import com.nextchaptersoftware.ciservice.handlers.circleci.CircleCiJobEventHandler
import com.nextchaptersoftware.ciservice.handlers.circleci.CircleCiWorkflowEventHandler
import com.nextchaptersoftware.ciservice.handlers.github.GitHubCheckRunEventHandler
import com.nextchaptersoftware.ciservice.handlers.github.GitHubCheckSuiteEventHandler
import com.nextchaptersoftware.event.queue.handlers.EventHandler

class CiEventHandler(
    private val buildEventHandler: BuildEventHandler,
    private val buildJobEventHandler: BuildJobEventHandler,
    private val buildTriageTaskHandler: BuildTriageTaskHandler,
    private val buildkiteJobEventHandler: BuildkiteJobEventHandler,
    private val buildkiteBuildEventHandler: BuildkiteBuildEventHandler,
    private val circleCiJobEventHandler: CircleCiJobEventHandler,
    private val circleCiWorkflowEventHandler: CircleCiWorkflowEventHandler,
    private val gitHubCheckRunEventHandler: GitHubCheckRunEventHandler,
    private val gitHubCheckSuiteEventHandler: GitHubCheckSuiteEventHandler,
) : EventHandler {

    override suspend fun handle(event: String): Boolean {
        when (val ciEvent = event.decode<CiEvent>()) {
            is CiEvent.BuildCompletedEvent -> buildEventHandler.handle(ciEvent)
            is CiEvent.BuildJobCompletedEvent -> buildJobEventHandler.handle(ciEvent)
            is CiEvent.BuildTriageTask -> buildTriageTaskHandler.handle(ciEvent)
            is CiEvent.WebhookBuildkiteBuildEvent -> buildkiteBuildEventHandler.handle(ciEvent.event)
            is CiEvent.WebhookBuildkiteJobEvent -> buildkiteJobEventHandler.handle(ciEvent.event)
            is CiEvent.WebhookCircleCiJobEvent -> circleCiJobEventHandler.handle(ciEvent.event)
            is CiEvent.WebhookCircleCiWorkflowEvent -> circleCiWorkflowEventHandler.handle(ciEvent.event)
            is CiEvent.WebhookGitHubCheckRunEvent -> gitHubCheckRunEventHandler.handle(ciEvent.event)
            is CiEvent.WebhookGitHubCheckSuiteEvent -> gitHubCheckSuiteEventHandler.handle(ciEvent.event)
        }
        return true
    }
}
