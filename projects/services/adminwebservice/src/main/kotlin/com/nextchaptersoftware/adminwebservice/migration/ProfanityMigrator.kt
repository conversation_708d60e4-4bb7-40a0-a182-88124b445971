package com.nextchaptersoftware.adminwebservice.migration

import com.nextchaptersoftware.db.common.Database
import com.nextchaptersoftware.db.models.PullRequestDAO
import com.nextchaptersoftware.db.models.PullRequestModel
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.ScmTeamId
import com.nextchaptersoftware.db.models.ThreadDAO
import com.nextchaptersoftware.db.models.ThreadModel
import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import kotlin.text.RegexOption.IGNORE_CASE
import kotlin.text.RegexOption.MULTILINE
import mu.KotlinLogging
import org.jetbrains.exposed.sql.select

private val LOGGER = KotlinLogging.logger {}

class ProfanityMigrator {
    private val profaneWordsRegex = Regex("fuck|shit|crap", setOf(IGNORE_CASE, MULTILINE))

    suspend fun censorTitles(
        teamId: ScmTeamId,
        repoId: RepoId,
    ) = withLoggingContextAsync(
        "teamId" to teamId,
        "repoId" to repoId,
    ) {
        censorThreadTitlesProfanity(
            teamId = teamId,
            repoId = repoId,
        )

        censorPullRequestTitleProfanity(
            teamId = teamId,
            repoId = repoId,
        )
    }

    @Suppress("MagicNumber")
    private suspend fun censorPullRequestTitleProfanity(
        teamId: ScmTeamId,
        repoId: RepoId,
    ) = withLoggingContextAsync(
        "teamId" to teamId,
        "repoId" to repoId,
    ) {
        Database.suspendedTransaction {
            val mapping = PullRequestModel
                .select(PullRequestModel.id, PullRequestModel.title)
                .where { PullRequestModel.repo eq repoId }
                .map { Pair(it[PullRequestModel.id], it[PullRequestModel.title]) }

            LOGGER.infoAsync(
                "mappingSize" to mapping.size,
            ) { "Mapping Size" }

            mapping.chunked(100).forEachIndexed { index, chunkedMap ->
                LOGGER.infoAsync(
                    "chunkIndex" to index,
                ) { "Processing chunk " }

                chunkedMap.forEach { (pullRequestId, pullRequestTitle) ->
                    if (profaneWordsRegex.containsMatchIn(pullRequestTitle)) {
                        val censoredPullRequestTitle = pullRequestTitle.replace(profaneWordsRegex, "stuff")
                        LOGGER.infoAsync(
                            "pullRequestId" to pullRequestId.value,
                            "pullRequestTitle" to pullRequestTitle,
                            "censoredPullRequestTitle" to censoredPullRequestTitle,
                        ) { "Censoring thread title" }
                        PullRequestDAO.findById(pullRequestId)?.let {
                            it.title = censoredPullRequestTitle
                        }
                    }
                }
            }
        }
    }

    @Suppress("MagicNumber")
    private suspend fun censorThreadTitlesProfanity(
        teamId: ScmTeamId,
        repoId: RepoId,
    ) = withLoggingContextAsync(
        "teamId" to teamId,
        "repoId" to repoId,
    ) {
        Database.suspendedTransaction {
            val mapping = ThreadModel
                .select(ThreadModel.id, ThreadModel.title)
                .where { ThreadModel.repo eq repoId }
                .map { Pair(it[ThreadModel.id], it[ThreadModel.title]) }

            LOGGER.infoAsync(
                "mappingSize" to mapping.size,
            ) { "Mapping Size" }

            mapping.chunked(100).forEachIndexed { index, chunkedMap ->
                LOGGER.infoAsync(
                    "chunkIndex" to index,
                ) { "Processing chunk " }

                chunkedMap.forEach { (threadId, threadTitle) ->
                    if (profaneWordsRegex.containsMatchIn(threadTitle)) {
                        val censoredThreadTitle = threadTitle.replace(profaneWordsRegex, "stuff")
                        LOGGER.infoAsync(
                            "threadId" to threadId.value,
                            "threadTitle" to threadTitle,
                            "censoredThreadTitle" to censoredThreadTitle,
                        ) { "Censoring thread title" }
                        ThreadDAO.findById(threadId)?.let {
                            it.title = censoredThreadTitle
                        }
                    }
                }
            }
        }
    }
}
