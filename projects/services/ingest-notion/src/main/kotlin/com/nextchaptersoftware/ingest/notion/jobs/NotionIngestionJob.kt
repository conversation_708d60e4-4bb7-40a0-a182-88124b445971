package com.nextchaptersoftware.ingest.notion.jobs

import com.nextchaptersoftware.notion.ingestion.services.NotionIngestionService
import com.nextchaptersoftware.service.BackgroundJob

class NotionIngestionJob(
    private val ingestionService: NotionIngestionService,
) : BackgroundJob {
    override val name: String
        get() = "Notion Ingestion Job"

    override suspend fun run() {
        ingestionService.run()
    }
}
