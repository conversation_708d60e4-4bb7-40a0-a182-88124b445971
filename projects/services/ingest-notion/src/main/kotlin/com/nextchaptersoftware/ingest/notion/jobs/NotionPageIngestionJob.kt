package com.nextchaptersoftware.ingest.notion.jobs

import com.nextchaptersoftware.notion.ingestion.services.NotionPageIngestionService
import com.nextchaptersoftware.service.BackgroundJob

class NotionPageIngestionJob(
    private val notionPageIngestionService: NotionPageIngestionService,
) : BackgroundJob {
    override val name: String
        get() = "Notion Page Ingestion Job"

    override suspend fun run() {
        notionPageIngestionService.run()
    }
}
