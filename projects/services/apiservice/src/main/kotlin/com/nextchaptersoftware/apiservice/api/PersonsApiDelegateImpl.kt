package com.nextchaptersoftware.apiservice.api

import com.nextchaptersoftware.api.PersonsApiDelegateInterface
import com.nextchaptersoftware.api.models.EmailPreferences
import com.nextchaptersoftware.api.models.OnboardingStatus
import com.nextchaptersoftware.api.models.OnboardingStatusUpdate
import com.nextchaptersoftware.api.models.Person as ApiPerson
import com.nextchaptersoftware.api.models.PersonOnboardingStatus
import com.nextchaptersoftware.api.models.PersonOnboardingStatusUpdate
import com.nextchaptersoftware.api.models.PersonPreferences
import com.nextchaptersoftware.api.models.PersonUpdate
import com.nextchaptersoftware.api.models.converters.asApiModel
import com.nextchaptersoftware.api.models.converters.asApiPersonModel
import com.nextchaptersoftware.api.models.converters.fromProductAgentHeaderOrNull
import com.nextchaptersoftware.api.services.PersonEmailPreferencesService
import com.nextchaptersoftware.api.services.PersonService
import com.nextchaptersoftware.api.utils.CallExtensions.addLatestModifiedToResponseHeader
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.Person
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.stores.PersonPreferencesStore
import com.nextchaptersoftware.db.stores.PersonStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.BadRequestException
import com.nextchaptersoftware.ktor.NotFoundException
import com.nextchaptersoftware.ktor.UserVisibleException
import com.nextchaptersoftware.models.identityId
import com.nextchaptersoftware.models.identityOrNull
import com.nextchaptersoftware.models.personId
import com.nextchaptersoftware.security.HMACAuthenticator
import com.nextchaptersoftware.types.EmailAddress
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.ApplicationCall
import io.ktor.server.routing.RoutingContext
import java.sql.SQLException
import org.openapitools.server.Resources
import org.openapitools.server.Resources.getPersonEmailPreferences
import org.openapitools.server.Resources.updatePersonEmailPreferences

class PersonsApiDelegateImpl(
    private val personService: PersonService,
    private val personEmailPreferencesService: PersonEmailPreferencesService,
    private val personStore: PersonStore = Stores.personStore,
    private val personPreferencesStore: PersonPreferencesStore = Stores.personPreferencesStore,
    private val intercomHMACAuthenticator: HMACAuthenticator,
    private val urlBuilderProvider: UrlBuilderProvider,
) : PersonsApiDelegateInterface {

    companion object {
        const val MAX_DISPLAY_NAME_LENGTH = 256
    }

    override suspend fun getPersonEmailPreferences(
        context: RoutingContext,
        input: getPersonEmailPreferences,
    ): EmailPreferences {
        return suspendedTransaction {
            personEmailPreferencesService.getOrPutForPerson(trx = this, personId = context.personId())
        }?.asDataModel()?.asApiModel() ?: throw NotFoundException()
    }

    override suspend fun getPersonEmails(context: RoutingContext, input: Resources.getPersonEmails): List<String> {
        return personStore.getTrustedPersonEmails(context.personId()).map { it.value }.sorted()
    }

    override suspend fun updatePersonEmailPreferences(
        context: RoutingContext,
        input: updatePersonEmailPreferences,
        body: EmailPreferences,
    ): EmailPreferences {
        return suspendedTransaction {
            personEmailPreferencesService.upsert(
                trx = this,
                personId = context.personId(),
                threadInviteEmails = body.threadInviteEmails,
                digestEmails = body.digestEmails,
                recommendedInviteeEmails = body.recommendedInviteeEmails,
            )
        }?.asDataModel()?.asApiModel() ?: throw NotFoundException()
    }

    override suspend fun getPersonV2(context: RoutingContext, input: Resources.getPersonV2): ApiPerson {
        return getPerson(call = context.call, personId = context.personId(), identityId = context.identityId)
    }

    private suspend fun getPerson(call: ApplicationCall, personId: PersonId, identityId: IdentityId): ApiPerson {
        return personService.getPerson(personId = personId)?.asApiPersonModel(
            call = call,
            identityId = identityId,
            intercomHMACAuthenticator = intercomHMACAuthenticator,
            urlBuilderProvider = urlBuilderProvider,
        ) ?: throw NotFoundException()
    }

    override suspend fun patchPerson(
        context: RoutingContext,
        input: Resources.patchPerson,
        body: PersonUpdate,
    ): ApiPerson {
        val personId = context.personId()

        // Display name
        body.displayName?.also { displayName ->
            if (displayName.isBlank() || displayName.length > MAX_DISPLAY_NAME_LENGTH) {
                throw UserVisibleException(
                    statusCode = HttpStatusCode.BadRequest,
                    title = "Failed to change name",
                    detail = "Name should be between 1 and $MAX_DISPLAY_NAME_LENGTH characters.",
                    url = null,
                )
            }
            personStore.updateDisplayName(id = personId, displayName = displayName)
        }

        // Primary email
        body.primaryEmail?.let { EmailAddress.of(it) }?.also { primaryEmail ->
            if (personStore.getTrustedPersonEmails(personId).contains(primaryEmail)) {
                runSuspendCatching {
                    personStore.updatePrimaryEmail(id = personId, email = primaryEmail)
                }.onFailure {
                    if (it is SQLException && it.message?.contains("duplicate key value violates unique constraint") == true) {
                        throw UserVisibleException(
                            statusCode = HttpStatusCode.BadRequest,
                            title = "Failed to change email",
                            detail = "Email (${body.primaryEmail}) already in use by another account.",
                            url = null,
                        )
                    } else {
                        throw it
                    }
                }
            } else {
                // Attempt to set primary email to untrusted email
                throw UserVisibleException(
                    statusCode = HttpStatusCode.BadRequest,
                    title = "Failed to change email",
                    detail = "Email (${body.primaryEmail}) is not allowed.",
                    url = null,
                )
            }
        }

        return getPerson(call = context.call, personId = personId, identityId = context.identityId)
    }

    override suspend fun getOnboardingStatus(
        context: RoutingContext,
        input: Resources.getOnboardingStatus,
        xUnblockedProductAgent: String?,
    ): OnboardingStatus {
        val person = context.identityOrNull()?.person?.let { personService.getPerson(trx = null, personId = it) }
            ?: throw NotFoundException()

        val agent = ProductAgentType.fromProductAgentHeaderOrNull(xUnblockedProductAgent)
            ?: throw BadRequestException()

        context.call.addLatestModifiedToResponseHeader(
            latestModifieds = listOf(person.modifiedAt),
            from = null,
        )

        return onboardingStatus(person = person, agent = agent)
    }

    override suspend fun updateOnboardingStatus(
        context: RoutingContext,
        input: Resources.updateOnboardingStatus,
        body: OnboardingStatusUpdate,
        xUnblockedProductAgent: String?,
    ): OnboardingStatus {
        val identity = context.identityOrNull()
            ?: throw NotFoundException()

        val personId = identity.person
            ?: throw NotFoundException()

        val agent = ProductAgentType.fromProductAgentHeaderOrNull(xUnblockedProductAgent)
            ?: throw BadRequestException()

        body.hasSeenTutorial?.also {
            personService.setHasSeenTutorial(personId = personId, agent = agent, hasSeenTutorial = it)
        }

        body.hasSeenTopFile?.also {
            personService.setHasSeenTopFile(personId = personId, agent = agent, hasSeenTopFile = it)
        }

        body.hasDismissedToast?.also {
            personService.setHasDismissedToast(personId = personId, agent = agent, hasDismissedToast = it)
        }

        body.hasInstalledHub?.also {
            personService.setHasInstalledHub(personId = personId, hasInstalledHub = it)
        }

        return personService.getPerson(trx = null, personId = personId)?.let { person ->
            context.call.addLatestModifiedToResponseHeader(latestModifieds = listOf(person.modifiedAt), from = null)
            onboardingStatus(person = person, agent = agent)
        } ?: throw NotFoundException()
    }

    override suspend fun getPersonOnboardingStatus(
        context: RoutingContext,
        input: Resources.getPersonOnboardingStatus,
    ): PersonOnboardingStatus {
        return getOnboardingStatus(personId = context.personId())
    }

    override suspend fun updatePersonOnboardingStatus(
        context: RoutingContext,
        input: Resources.updatePersonOnboardingStatus,
        body: PersonOnboardingStatusUpdate,
    ): PersonOnboardingStatus {
        val personId = context.personId()
        body.hasInstalledDesktop?.let { personStore.setHasInstalledDesktop(id = personId, value = it) }
        body.hasSeenDocs?.let { personStore.setHasSeenDocs(id = personId, value = it) }
        body.hasInstalledIDEPlugins?.let { personStore.setHasInstalledIDEPlugins(id = personId, value = it) }
        body.hasSeenAnswersTutorial?.let { personStore.setHasSeenAnswersTutorial(id = personId, value = it) }
        return getOnboardingStatus(personId = personId)
    }

    private suspend fun getOnboardingStatus(personId: PersonId): PersonOnboardingStatus {
        val person = requireNotNull(personStore.findById(id = personId))
        return PersonOnboardingStatus(
            hasInstalledDesktop = person.hasInstalledDesktop,
            hasSeenDocs = person.hasSeenDocs,
            hasSeenAnswersTutorial = person.hasSeenAnswersTutorial,
        )
    }

    override suspend fun getPersonPreferences(
        context: RoutingContext,
        input: Resources.getPersonPreferences,
    ): PersonPreferences {
        return personPreferencesStore.getOrCreate(personId = context.personId()).let {
            PersonPreferences(
                enableCiTriage = it.enableCiTriage,
                incognitoMode = it.incognitoMode,
                hideIncognitoBanner = it.hideIncognitoBanner,
            )
        }
    }

    override suspend fun updatePersonPreferences(
        context: RoutingContext,
        input: Resources.updatePersonPreferences,
        body: PersonPreferences,
    ) {
        personPreferencesStore.upsert(
            personId = context.personId(),
            enableCiTriage = body.enableCiTriage,
            incognitoMode = body.incognitoMode,
            hideIncognitoBanner = body.hideIncognitoBanner,
        )
    }
}

private fun onboardingStatus(
    person: Person,
    agent: ProductAgentType,
): OnboardingStatus = when (agent) {
    ProductAgentType.VSCode -> OnboardingStatus(
        hasSeenTutorial = person.hasSeenTutorialVSCode,
        hasSeenTopFile = person.hasSeenTopFileVSCode,
        hasCreatedNote = person.hasCreatedNoteVSCode,
        hasDismissedToast = person.hasDismissedToastVSCode,
        hasInstalledHub = person.hasInstalledHub,
        hasInstalledDesktop = person.hasInstalledDesktop,
    )

    ProductAgentType.IntelliJ -> OnboardingStatus(
        hasSeenTutorial = person.hasSeenTutorialIntellij,
        hasSeenTopFile = person.hasSeenTopFileIntellij,
        hasCreatedNote = person.hasCreatedNoteIntellij,
        hasDismissedToast = person.hasDismissedToastIntellij,
        hasInstalledHub = person.hasInstalledHub,
        hasInstalledDesktop = person.hasInstalledDesktop,
    )

    ProductAgentType.Hub -> OnboardingStatus(
        hasSeenTutorial = person.hasSeenTutorialHub,
        hasInstalledHub = person.hasInstalledHub,
    )

    ProductAgentType.Desktop,
    ProductAgentType.Dashboard,
    ProductAgentType.Landing,
    ProductAgentType.Mcp,
        -> OnboardingStatus(
        hasInstalledHub = person.hasInstalledHub,
        hasInstalledDesktop = person.hasInstalledDesktop,
    )

    ProductAgentType.SafariExtension,
    ProductAgentType.ChromeExtension,
    ProductAgentType.IOS,
    ProductAgentType.Unknown,
    ProductAgentType.Slack,
        -> throw BadRequestException()
}
