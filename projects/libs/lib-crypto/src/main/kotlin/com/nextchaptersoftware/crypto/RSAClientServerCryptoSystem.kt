package com.nextchaptersoftware.crypto

import com.nextchaptersoftware.crypto.types.Ciphertext
import com.nextchaptersoftware.utils.Base64.base64DecodeAsByteArray
import com.sksamuel.hoplite.Secret
import java.security.Security
import org.bouncycastle.crypto.AsymmetricBlockCipher
import org.bouncycastle.crypto.digests.SHA256Digest
import org.bouncycastle.crypto.encodings.OAEPEncoding
import org.bouncycastle.crypto.engines.RSAEngine
import org.bouncycastle.crypto.util.PrivateKeyFactory
import org.bouncycastle.crypto.util.PublicKeyFactory

/**
 * Standard browser crypto libraries will use OAEP PADDING+SHA256
 * BouncyCastle by default uses NO padding in Java land.
 * To get around that, we use this custom RSA to handle client-sourced encrypted content.
 */
object RSAClientServerCryptoSystem {

    init {
        Security.addProvider(BouncyCastleProviderSingleton.INSTANCE)
    }

    class RSAEncryption(
        publicKeyString: String,
    ) : Encryption {
        private val cipher: AsymmetricBlockCipher

        init {
            val asymmetricKeyParameter = PublicKeyFactory.createKey(publicKeyString.base64DecodeAsByteArray())
            val engine = RSAEngine()
            cipher = OAEPEncoding(engine, SHA256Digest())
            cipher.init(true, asymmetricKeyParameter)
        }

        override fun encrypt(plainText: Secret): Ciphertext = synchronized(cipher) {
            val data = plainText.value.toByteArray()
            return Ciphertext(cipher.processBlock(data, 0, data.size))
        }
    }

    class RSADecryption(
        privateKeyString: String,
    ) : Decryption {
        private val cipher: AsymmetricBlockCipher

        init {
            val asymmetricKeyParameter = PrivateKeyFactory.createKey(privateKeyString.base64DecodeAsByteArray())
            val engine = RSAEngine()
            cipher = OAEPEncoding(engine, SHA256Digest())
            cipher.init(false, asymmetricKeyParameter)
        }

        override fun decrypt(ciphertext: Ciphertext): Secret = synchronized(cipher) {
            val result = cipher.processBlock(ciphertext.value, 0, ciphertext.value.size)
            return Secret(String(result))
        }
    }
}
