package com.nextchaptersoftware.embedding.models

import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.types.Hash
import com.nextchaptersoftware.utils.asUUID
import java.util.UUID
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable

/**
 * A compound key for a Serverless Pinecone document.
 */
@Serializable
data class EmbeddingDocumentKey(
    @Contextual val installationId: InstallationId?,
    @Contextual val groupId: UUID?,
    val revision: Int? = null,
    @Contextual val sourceId: UUID,
    val contentHash: Hash = BLANK_HASH,
    val partitionNumber: Int = 0,
) {
    init {
        validate()
    }

    companion object {
        private const val SEPARATOR_SYMBOL = "|"
        private const val EMPTY_SYMBOL = ""

        val BLANK_HASH = Hash.empty()
        private val BLANK_UUID = UUID.fromString("00000000-0000-0000-0000-000000000000")
        val BLANK_KEY = EmbeddingDocumentKey(
            installationId = BLANK_UUID.let(::InstallationId),
            groupId = BLANK_UUID,
            revision = 0,
            sourceId = BLANK_UUID,
            contentHash = BLANK_HASH,
            partitionNumber = 0,
        )

        @Suppress("MagicNumber")
        fun fromKey(key: String): EmbeddingDocumentKey {
            return key.split(SEPARATOR_SYMBOL).let { parts ->
                require(parts.size == 6)
                EmbeddingDocumentKey(
                    installationId = parts[0].let { if (it == EMPTY_SYMBOL) null else it.let(InstallationId::fromString) },
                    groupId = parts[1].let { if (it == EMPTY_SYMBOL) null else it.asUUID() },
                    revision = parts[2].let { if (it == EMPTY_SYMBOL) null else it.toInt() },
                    sourceId = parts[3].asUUID(),
                    contentHash = Hash.parse(parts[4]),
                    partitionNumber = parts[5].toInt(),
                )
            }
        }

        fun generateRandom() = EmbeddingDocumentKey(
            installationId = InstallationId.random(),
            groupId = UUID.randomUUID(),
            revision = 0,
            sourceId = UUID.randomUUID(),
            contentHash = BLANK_HASH,
            partitionNumber = 0,
        )

        fun fromKeyOrNull(key: String): EmbeddingDocumentKey? {
            return runSuspendCatching {
                fromKey(key)
            }.getOrNull()
        }

        internal fun installationIdPrefix(installationId: InstallationId): String {
            return BLANK_KEY.copy(installationId = installationId).installationIdPrefix
        }

        fun groupIdPrefix(installationId: InstallationId?, groupId: UUID, revision: Int?): String {
            return BLANK_KEY.copy(installationId = installationId, groupId = groupId, revision = revision).groupIdPrefix
        }

        @Suppress("MagicNumber")
        fun fromSourceIdPrefix(prefix: String): EmbeddingDocumentKey {
            return prefix.removeSuffix(SEPARATOR_SYMBOL).split(SEPARATOR_SYMBOL).let { parts -> // Remove trailing separator
                require(parts.size == 4)
                EmbeddingDocumentKey(
                    installationId = parts[0].let { if (it == EMPTY_SYMBOL) null else it.let(InstallationId::fromString) },
                    groupId = parts[1].let { if (it == EMPTY_SYMBOL) null else it.asUUID() },
                    revision = parts[2].let { if (it == EMPTY_SYMBOL) null else it.toInt() },
                    sourceId = parts[3].asUUID(),
                    contentHash = BLANK_HASH,
                    partitionNumber = 0,
                )
            }
        }

        fun installationIdFromSourceIdPrefix(prefix: String): InstallationId? {
            return fromSourceIdPrefix(prefix).installationId
        }

        fun groupIdFromSourceIdPrefix(prefix: String): UUID? {
            return fromSourceIdPrefix(prefix).groupId
        }

        fun sourceIdFromSourceIdPrefix(prefix: String): UUID {
            return fromSourceIdPrefix(prefix).sourceId
        }
    }

    /**
     * Serializes this compound key to a string.
     */
    val asKey: String
        get() = listOf(
            installationId?.toString(),
            groupId?.toString(),
            revision?.toString(),
            sourceId.toString(),
            contentHash.toString(),
            partitionNumber.toString(),
        ).onEach {
            it?.also {
                require(it.isNotBlank())
                require(it != null.toString())
                require(it.contains(SEPARATOR_SYMBOL).not())
            }
        }.joinToString(SEPARATOR_SYMBOL) {
            it ?: EMPTY_SYMBOL
        }

    /**
     * Used to list all documents for the same installation.
     */
    val installationIdPrefix: String
        get() {
            requireNotNull(installationId)
            return listOf(
                installationId.toString(),
            ).joinToString(separator = SEPARATOR_SYMBOL, postfix = SEPARATOR_SYMBOL)
        }

    /**
     * Used to list all documents for the same group.
     */
    val groupIdPrefix: String
        get() {
            requireNotNull(groupId)
            return listOf(
                installationId?.toString() ?: EMPTY_SYMBOL,
                groupId.toString(),
                revision?.toString() ?: EMPTY_SYMBOL,
            ).joinToString(separator = SEPARATOR_SYMBOL, postfix = SEPARATOR_SYMBOL)
        }

    /**
     * Used to list all documents for the same source.
     */
    val sourceIdPrefix: String
        get() = listOf(
            installationId?.toString() ?: EMPTY_SYMBOL,
            groupId?.toString() ?: EMPTY_SYMBOL,
            revision?.toString() ?: EMPTY_SYMBOL,
            sourceId.toString(),
        ).joinToString(separator = SEPARATOR_SYMBOL, postfix = SEPARATOR_SYMBOL)

    /**
     * Every document must have either an installationId or a groupId.
     * Revision is optional, and is used to distinguish between different versions of the same installation or group.
     *
     *   Document Type   | Insight Type       | Provider             | InstallationId    | GroupId           | Revision
     *   ----------------|--------------------|----------------------|-------------------|-------------------|-----------
     *   Code            | SourceCode         | <SCMs>               | UUID(InsightType) | repoId            | integer
     *                   |                    |                      |                   |                   |
     *   Documentation   | Discussion         | GitHub               | UUID(InsightType) | repoId            | -
     *   Documentation   | Discussion         | GitHubEnterprise     | UUID(InsightType) | repoId            | -
     *   Documentation   | Documentation      | Coda                 | installationId    | codaResourceId    | -
     *   Documentation   | Documentation      | Confluence           | installationId    | confluenceSpaceId | -
     *   Documentation   | Documentation      | ConfluenceDataCenter | installationId    | confluenceSpaceId | -
     *   Documentation   | Documentation      | CustomIntegration    | installationId    | collectionId      | -
     *   Documentation   | Documentation      | GoogleDrive          | installationId    | googleDriveFileId | -
     *   Documentation   | Documentation      | GoogleDriveWorkspace | installationId    | googleDriveFileId | -
     *   Documentation   | Documentation      | Notion               | installationId    | -                 | -
     *   Documentation   | Documentation      | StackOverflowTeams   | installationId    | -                 | -
     *   Documentation   | Documentation      | Web                  | installationId    | siteId            | -
     *   Documentation   | Issues             | Jira                 | installationId    | projectId         | -
     *   Documentation   | Issues             | JiraDataCenter       | installationId    | projectId         | -
     *                   |                    |                      |                   |                   |
     *   PullRequest     | PullRequest        | <SCMs>               | UUID(InsightType) | repoId            | -
     *                   |                    |                      |                   |                   |
     *   Thread          | Answer             | Unblocked            | UUID(InsightType) | -                 | -
     *   Thread          | Issue              | GitHub               | UUID(InsightType) | repoId            | -
     *   Thread          | Issue              | GitHubEnterprise     | UUID(InsightType) | repoId            | -
     *   Thread          | Issue              | Linear               | installationId    | linearTeamId      | -
     *   Thread          | PullRequestComment | <SCMs>               | UUID(InsightType) | repoId            | -
     *   Thread          | Slack              | Slack                | installationId    | channelId         | -
     *
     * ### Future Direction
     * - Everything must have a real PG [InstallationId]
     * - Replace all non-Answer Thread with Documentation DocumentType
     */
    private fun validate() {
        require(installationId != null || groupId != null) {
            "At least one of installationId or groupId must be non-null"
        }
        require(partitionNumber >= 0) {
            "partitionNumber must be non-negative"
        }
        revision?.also {
            require(revision >= 0) {
                "revision must be non-negative if present"
            }
        }
    }
}
