package com.nextchaptersoftware.slack.bot.services.notification

import com.nextchaptersoftware.db.models.PlanCapabilityType
import com.nextchaptersoftware.db.stores.IdentityStore
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.stores.SlackTeamStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.environment.DashboardUrlBuilder
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.plan.capabilities.PlanCapabilitiesService
import com.nextchaptersoftware.slack.api.SlackApiProvider
import com.nextchaptersoftware.slack.api.models.SlackPermission
import com.nextchaptersoftware.slack.bot.invites.SlackAccountInviteUrlBuilder
import com.nextchaptersoftware.slack.bot.models.notification.SlackBotMemberNotificationContext
import com.nextchaptersoftware.slack.bot.models.notification.SlackBotMemberNotificationInputContext
import com.nextchaptersoftware.slack.bot.models.payload.SlackBotAppHomeOpenedPayload
import com.nextchaptersoftware.slack.bot.models.payload.SlackBotPayload
import com.nextchaptersoftware.slack.bot.models.payload.toAttachmentPayload
import com.nextchaptersoftware.slack.bot.models.payload.toBlockPayload
import com.nextchaptersoftware.slack.services.SlackDeepLinkService
import com.nextchaptersoftware.slack.services.SlackTokenService
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class SlackBotAppHomeOpenedWelcomeDisconnectedNotificationService(
    private val slackTokenService: SlackTokenService,
    private val slackApiProvider: SlackApiProvider,
    private val planCapabilitiesService: PlanCapabilitiesService,
    private val slackAccountInviteUrlBuilder: SlackAccountInviteUrlBuilder,
    identityStore: IdentityStore = Stores.identityStore,
    slackTeamStore: SlackTeamStore = Stores.slackTeamStore,
    scmTeamStore: ScmTeamStore = Stores.scmTeamStore,
    slackDeepLinkService: SlackDeepLinkService,
) : BaseSlackBotMemberNotificationService<SlackBotMemberNotificationInputContext.AppHomeOpenedWelcomeInputContext>(
    slackBotMemberContextResolver = SlackBotMemberContextResolver(
        identityStore = identityStore,
        slackTeamStore = slackTeamStore,
        scmTeamStore = scmTeamStore,
        slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
        slackDeepLinkService = slackDeepLinkService,
    ),
) {
    override suspend fun notify(input: SlackBotMemberNotificationInputContext.AppHomeOpenedWelcomeInputContext): Boolean =
        withLoggingContextAsync(
            "slackExternalTeamId" to input.slackExternalTeamId,
            "slackExternalChannelId" to input.slackExternalChannelId,
            "slackUserId" to input.slackUserId,
        ) {
            val slackBotMemberNotificationContext = getSlackBotMemberNotificationContext(input = input)
                ?: run {
                    LOGGER.errorAsync { "Failed to get slack bot member notification context" }
                    return@withLoggingContextAsync false
                }

            if (slackBotMemberNotificationContext.isConnected) {
                LOGGER.debugAsync { "Slack team member is connected" }
                return@withLoggingContextAsync false
            }

            if (
                !planCapabilitiesService.hasCapability(
                    orgId = slackBotMemberNotificationContext.slackChannelInfo.org.idValue,
                    planCapabilityType = PlanCapabilityType.SlackPrivateScopes,
                )
            ) {
                LOGGER.debugAsync { "Skipping generate private scopes slack bot welcome due to private scopes being disabled" }
                return@withLoggingContextAsync false
            }

            val payload = createSlackBotPayload(
                input = input,
                slackBotMemberNotificationContext = slackBotMemberNotificationContext,
            )

            executeSlackApiCall {
                slackApiProvider.slackChatApi.postChatMessage(
                    token = slackTokenService.getSlackToken(
                        slackTeamId = slackBotMemberNotificationContext.slackChannelInfo.slackTeam.idValue,
                        permissions = listOf(SlackPermission.CHAT_WRITE),
                    ).value,
                    channelId = input.slackUserId,
                    payload = payload.attachments?.toAttachmentPayload() ?: payload.blocks.toBlockPayload(),
                )
            }

            return@withLoggingContextAsync true
        }

    private suspend fun createSlackBotPayload(
        input: SlackBotMemberNotificationInputContext.AppHomeOpenedWelcomeInputContext,
        slackBotMemberNotificationContext: SlackBotMemberNotificationContext,
    ): SlackBotPayload {
        val connectAccountUrl = getSignInInviteUrl(
            slackExternalChannelId = input.slackExternalChannelId,
            slackThreadTs = null,
            slackTs = null,
            slackBotMemberNotificationContext = slackBotMemberNotificationContext,
        )
        val dashboardUrl = DashboardUrlBuilder().build()
        return SlackBotAppHomeOpenedPayload.SlackBotAppHomeOpenedDisconnectedWelcomeBlock(
            dashboardUrl = dashboardUrl,
            connectAccountUrl = connectAccountUrl,
            integrations = slackBotMemberNotificationContext.integrations,
        )
    }
}
