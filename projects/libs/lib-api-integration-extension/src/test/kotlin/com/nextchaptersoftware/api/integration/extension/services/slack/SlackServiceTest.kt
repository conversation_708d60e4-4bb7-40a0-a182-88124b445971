package com.nextchaptersoftware.api.integration.extension.services.slack

import com.nextchaptersoftware.clientconfig.ClientConfigService
import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgBilling
import com.nextchaptersoftware.db.ModelBuilders.makePlan
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.ModelBuilders.makeSlackTeam
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.OrgBillingDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PlanDAO
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.SlackTeamDAO
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.plan.capabilities.PlanCapabilitiesServiceImpl
import com.nextchaptersoftware.slack.bot.events.queue.enqueue.SlackBotEventEnqueueService
import com.nextchaptersoftware.slack.events.queue.enqueue.SlackEventEnqueueService
import com.nextchaptersoftware.slack.services.SlackChannelAccessService
import com.nextchaptersoftware.slack.services.SlackChannelFilterService
import com.nextchaptersoftware.slack.services.SlackChannelPatternPreferencesService
import com.nextchaptersoftware.slack.services.SlackChannelPreferencesService
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock

class SlackServiceTest : DatabaseTestsBase() {
    private lateinit var org: OrgDAO
    private lateinit var plan: PlanDAO
    private lateinit var orgBilling: OrgBillingDAO
    private lateinit var scmTeam: ScmTeamDAO
    private lateinit var slackTeam: SlackTeamDAO
    private lateinit var slackInstallation: InstallationDAO

    private val slackTeamStore = Stores.slackTeamStore
    private val slackChannelStore = Stores.slackChannelStore
    private val slackChannelPreferencesStore = Stores.slackChannelPreferencesStore
    private val slackChannelPatternPreferencesStore = Stores.slackChannelPatternPreferencesStore
    private val clientConfigService = ClientConfigService()
    private val planCapabilitiesService = PlanCapabilitiesServiceImpl()
    private val slackEventEnqueueService: SlackEventEnqueueService = mock()
    private val slackBotEventEnqueueService: SlackBotEventEnqueueService = mock()
    private val slackChannelAccessService = SlackChannelAccessService(
        planCapabilitiesService = planCapabilitiesService,
    )
    private val slackChannelPreferencesService = SlackChannelPreferencesService(
        slackChannelAccessService = slackChannelAccessService,
    )
    private val slackChannelPatternPreferencesService = SlackChannelPatternPreferencesService(
        slackChannelPatternPreferencesStore = slackChannelPatternPreferencesStore,
    )
    private val slackChannelFilterService = SlackChannelFilterService(
        slackChannelStore = slackChannelStore,
        slackChannelPreferencesStore = slackChannelPreferencesStore,
        clientConfigService = clientConfigService,
        slackChannelAccessService = slackChannelAccessService,
    )

    private val slackRecommendedChannelsService = SlackRecommendedChannelsService(
        planCapabilitiesService = planCapabilitiesService,
    )
    private val slackSearchService = SlackSearchService(
        planCapabilitiesService = planCapabilitiesService,
    )

    private val slackConfigurationService = SlackConfigurationService(
        slackTeamStore = slackTeamStore,
        slackEventEnqueueService = slackEventEnqueueService,
        slackBotEventEnqueueService = slackBotEventEnqueueService,
        slackChannelFilterService = slackChannelFilterService,
        slackChannelPreferencesService = slackChannelPreferencesService,
        slackChannelPatternPreferencesService = slackChannelPatternPreferencesService,
    )

    private val service = SlackService(
        slackTeamStore = slackTeamStore,
        slackSearchService = slackSearchService,
        slackConfigurationService = slackConfigurationService,
        slackRecommendedChannelsService = slackRecommendedChannelsService,
    )

    private suspend fun setup() {
        org = makeOrg()
        plan = makePlan()
        orgBilling = makeOrgBilling(org = org, plan = plan)

        scmTeam = makeScmTeam(org = org)
        slackInstallation = makeInstallation(
            org = org,
            provider = Provider.Slack,
        )
        slackTeam = makeSlackTeam(
            botScope = "asdf",
            userScope = "1234",
            org = org,
            installation = slackInstallation,
            slackExternalTeamId = slackInstallation.installationExternalId,
        )
    }

    @Test
    fun getSlackTeams() = suspendingDatabaseTest {
        setup()
        assertThat(service.getInstalledSlackTeams(orgIds = listOf(org.idValue)).single().id).isEqualTo(slackTeam.idValue)
        assertThat(service.getInstalledSlackTeams(orgIds = listOf(OrgId.random()))).isEmpty()
    }
}
