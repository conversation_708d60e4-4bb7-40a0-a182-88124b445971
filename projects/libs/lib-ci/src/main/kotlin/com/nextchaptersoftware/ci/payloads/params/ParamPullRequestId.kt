package com.nextchaptersoftware.ci.payloads.params

import com.nextchaptersoftware.ci.payloads.CiTriageOptions
import com.nextchaptersoftware.db.models.PullRequest
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.db.stores.Stores.pullRequestStore
import com.nextchaptersoftware.log.kotlin.traceAsync
import com.nextchaptersoftware.utils.KotlinUtils.required

private val LOGGER = mu.KotlinLogging.logger {}

interface ParamPullRequestId {

    val pullRequestId: PullRequestId

    suspend fun pullRequest(
        options: CiTriageOptions? = null,
    ): PullRequest? {
        val pullRequest = pullRequestStore.findById(prId = pullRequestId).required { "Missing Pull Request" }
        if (pullRequest.isTriageMuted) {
            if (options?.disablePullRequestMuteCheck == true) {
                LOGGER.traceAsync { "Pull Request is Muted, but check is disabled.." }
            } else {
                LOGGER.traceAsync { "Pull Request is Muted" }
                return null
            }
        }
        return pullRequest
    }
}
