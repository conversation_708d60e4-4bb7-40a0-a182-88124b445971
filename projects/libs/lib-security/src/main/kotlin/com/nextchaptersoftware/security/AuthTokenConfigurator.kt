package com.nextchaptersoftware.security

import com.nextchaptersoftware.api.client.models.ApiError
import com.nextchaptersoftware.api.client.models.ApiErrorCause
import com.nextchaptersoftware.security.jwt.Jwt
import com.nextchaptersoftware.utils.asUUIDOrNull
import io.ktor.http.HttpStatusCode
import io.ktor.server.auth.AuthenticationConfig
import io.ktor.server.auth.jwt.jwt
import io.ktor.server.request.httpMethod
import io.ktor.server.request.path
import io.ktor.server.response.respond

class AuthTokenConfigurator(
    private val jwt: Jwt,
) : AuthProviderConfigurator {

    override fun configure(config: AuthenticationConfig) {
        config.jwt("bearerAuth") {
            validate { credential ->
                val orgId = this.parameters["teamId"]?.asUUIDOrNull()

                jwt.authTokenValidator(
                    orgId = orgId,
                    credential = credential,
                    method = request.httpMethod,
                    path = request.path(),
                    forbidden = { request.call.respond(HttpStatusCode.Forbidden) },
                    forbiddenAuthRequired = {
                        request.call.respond(
                            HttpStatusCode.Forbidden,
                            ApiError(
                                status = HttpStatusCode.Forbidden.value,
                                cause = ApiErrorCause.needsSsoAuthentication,
                            ),
                        )
                    },
                )
            }

            verifier(jwt.authTokenVerifier)
        }
    }
}
