package com.nextchaptersoftware.confluence.ingestion.services

import com.nextchaptersoftware.activemq.models.MessagePriority
import com.nextchaptersoftware.confluence.api.ConfluenceApiProvider
import com.nextchaptersoftware.confluence.api.ConfluencePagesApi
import com.nextchaptersoftware.confluence.ingestion.services.Utils.shouldIngest
import com.nextchaptersoftware.confluence.models.toPage
import com.nextchaptersoftware.db.models.ConfluenceIngestion
import com.nextchaptersoftware.db.models.ConfluenceSite
import com.nextchaptersoftware.db.models.ConfluenceSpace
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.stores.IngestionStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.ingestion.utils.Utils.priority
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.redis.lock.RedisLock
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.takeWhile
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

@Suppress("TooGenericExceptionCaught")
class ConfluencePageIngestionService(
    private val confluenceApiProvider: ConfluenceApiProvider,
    private val embeddingService: ConfluenceEmbeddingService,
    private val ingestionStore: IngestionStore = Stores.ingestionStore,
) {
    suspend fun ingest(
        installation: Installation,
        site: ConfluenceSite,
        ingestion: ConfluenceIngestion,
        confluenceSpaceProvider: (String) -> ConfluenceSpace?,
        lock: RedisLock,
    ): Boolean {
        var done = true

        confluenceApiProvider.confluencePagesApi.streamPages(
            url = ingestion.pagesNextUrl ?: ConfluencePagesApi.url(baseUrl = site.userAuthBaseUrl),
            installationId = installation.id,
        ).takeWhile { batch ->
            lock.renew().also {
                if (!it) {
                    LOGGER.warnAsync { "ConfluencePageIngestionService.ingest: lock expired" }
                    done = false
                    return@takeWhile false
                }
            }

            batch.items.any { it.toPage().version.shouldIngest(ingestion) }
        }.onEach { batch ->
            batch.items.map { it.toPage() }.filter {
                it.version.shouldIngest(ingestion)
            }.forEach { page ->
                confluenceSpaceProvider(page.spaceId)?.let {
                    try {
                        embeddingService.embedPage(
                            orgId = installation.orgId,
                            installationId = ingestion.installationId,
                            confluenceSite = site,
                            confluenceSpaceId = it.id,
                            page = page,
                            priority = ingestion.priority(),
                        )
                    } catch (t: Throwable) {
                        LOGGER.errorAsync(t, "pageId" to page.id) { "Failed to ingest Confluence page" }
                    }
                }
            }

            ingestionStore.setConfluencePagesNextUrl(
                installationId = ingestion.installationId,
                nextUrl = batch.nextCursor,
            )
        }.catch {
            LOGGER.errorAsync(it) { "Error while ingesting confluence pages" }
            done = false
        }.collect()

        if (done) {
            ingestionStore.setConfluencePagesNextUrl(
                installationId = ingestion.installationId,
                nextUrl = null,
            )
        }

        return done
    }

    suspend fun ingestForSpace(
        installation: Installation,
        site: ConfluenceSite,
        space: ConfluenceSpace,
        identityId: IdentityId,
        confluenceSpaceProvider: (String) -> ConfluenceSpace?,
    ) = withLoggingContextAsync(
        "installationId" to installation.id,
        "spaceId" to space.id,
        "spaceName" to space.spaceName,
        "identityId" to identityId,
    ) {
        require(installation.id == site.installationId) { "Installation ID mismatch" }

        confluenceApiProvider.confluencePagesApi.streamPagesForIdentity(
            url = ConfluencePagesApi.url(baseUrl = site.userAuthBaseUrl, spaceId = space.spaceId),
            identityId = identityId,
        ).onEach { batch ->
            LOGGER.debugAsync("batchSize" to batch.items.size) { "ConfluenceIngestionService.ingestForSpace" }

            batch.items.map { it.toPage() }.forEach { page ->
                confluenceSpaceProvider(page.spaceId)?.let {
                    try {
                        embeddingService.embedPage(
                            orgId = installation.orgId,
                            installationId = installation.id,
                            confluenceSite = site,
                            confluenceSpaceId = it.id,
                            page = page,
                            priority = MessagePriority.DEFAULT,
                        )
                    } catch (t: Throwable) {
                        LOGGER.errorAsync(t, "pageId" to page.id) { "Failed to ingest Confluence page" }
                    }
                }
            }
        }.catch {
            LOGGER.errorAsync(it) { "Error while ingesting confluence pages for space" }
        }.collect()

        LOGGER.debugAsync { "ConfluencePageIngestionService.ingestForSpace done" }
    }
}
