package com.nextchaptersoftware.service

import com.nextchaptersoftware.event.queue.dequeue.EventDequeueService
import com.nextchaptersoftware.service.lifecycle.ShutdownHook

interface BackgroundJob : ShutdownHook {
    val name: String

    suspend fun run()

    override suspend fun shutdownGracefully() {}

    suspend fun terminate() {}
}

/**
 * Creates a background job that processes events using the provided `EventDequeueService`.
 *
 * @param name the name of the background job
 * @param eventDequeueService the service responsible for processing events
 * @return an instance of `BackgroundJob`
 */
fun createBackgroundJob(name: String, eventDequeueService: EventDequeueService): BackgroundJob {
    return object : BackgroundJob {
        override val name: String = name

        override suspend fun run() = eventDequeueService.process()
    }
}
