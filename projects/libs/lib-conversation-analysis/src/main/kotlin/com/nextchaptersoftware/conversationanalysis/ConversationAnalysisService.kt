package com.nextchaptersoftware.conversationanalysis

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.bot.services.BotAccountService
import com.nextchaptersoftware.bot.services.InstallationBotAccountService
import com.nextchaptersoftware.conversationanalysis.ml.DataPoint
import com.nextchaptersoftware.conversationanalysis.ml.calculateKMeansClusters
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.ClusterInfo
import com.nextchaptersoftware.db.models.ClusteringResults
import com.nextchaptersoftware.db.models.ConversationAnalysis
import com.nextchaptersoftware.db.models.ConversationAnalysisId
import com.nextchaptersoftware.db.models.ConversationAnalysisResultHumanFeedbackId
import com.nextchaptersoftware.db.models.ConversationAnalysisType
import com.nextchaptersoftware.db.models.EmbeddingModel
import com.nextchaptersoftware.db.models.ExecutionState
import com.nextchaptersoftware.db.models.ExecutiveSummary
import com.nextchaptersoftware.db.models.FeedbackType
import com.nextchaptersoftware.db.models.MLInferenceEngine
import com.nextchaptersoftware.db.models.MLInferenceTemplate
import com.nextchaptersoftware.db.models.MLInferenceTemplateKind
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.MessageFeedbackId
import com.nextchaptersoftware.db.models.MessageId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.ThreadId
import com.nextchaptersoftware.db.stores.ConversationAnalysisAggregate
import com.nextchaptersoftware.db.stores.ConversationAnalysisResultHumanFeedbackStore
import com.nextchaptersoftware.db.stores.ConversationAnalysisStore
import com.nextchaptersoftware.db.stores.MessageFeedbackStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.environment.StandardUrlBuilderProvider
import com.nextchaptersoftware.event.queue.enqueue.EventEnqueueService
import com.nextchaptersoftware.event.queue.handlers.EventHandler
import com.nextchaptersoftware.insight.index.ThreadInsightIndexContentService
import com.nextchaptersoftware.insight.index.model.InsightIndexContent
import com.nextchaptersoftware.insight.index.model.ThreadInsightIndexContentModel
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.ml.completion.InstrumentedCompletionService
import com.nextchaptersoftware.ml.embedding.core.models.MLVector
import com.nextchaptersoftware.ml.embedding.services.IEmbeddingService
import com.nextchaptersoftware.ml.inference.services.template.MLInferenceTemplateService
import java.nio.ByteBuffer
import java.nio.ByteOrder
import kotlin.time.Duration
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.datetime.Instant
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.SerializationException
import mu.KotlinLogging
import org.apache.commons.math3.ml.clustering.CentroidCluster

private val LOGGER = KotlinLogging.logger {}

@SerialName("FeedbackAnalysisRequest")
@Serializable
private data class FeedbackAnalysisRequest(
    val id: ConversationAnalysisResultHumanFeedbackId,
    @Contextual val feedbackId: MessageFeedbackId,
    @Contextual val analysisId: ConversationAnalysisId,
    val feedbackCreatedAt: Instant,
    val orgId: OrgId?,
    val orgMemberId: OrgMemberId,
    val messageId: MessageId,
    val threadId: ThreadId,
    val feedbackDescription: String?,
    val feedbackType: FeedbackType,
)

private fun FeedbackType.toConversationAnalysisType() = when (this) {
    FeedbackType.Positive -> ConversationAnalysisType.FEEDBACK_POSITIVE
    FeedbackType.Negative -> ConversationAnalysisType.FEEDBACK_NEGATIVE
    else -> throw IllegalArgumentException("Unsupported feedback type: $this")
}

private const val INFERENCE_PARALLELISM = 4

private const val SUMMARY_LIMIT_FOR_CLUSTER_LABELING = 50

class ConversationAnalysisService(
    private val messageFeedbackStore: MessageFeedbackStore = Stores.messageFeedbackStore,
    private val conversationAnalysisStore: ConversationAnalysisStore = Stores.conversationAnalysisStore,
    private val conversationAnalysisResultHumanFeedbackStore: ConversationAnalysisResultHumanFeedbackStore =
        Stores.conversationAnalysisResultHumanFeedbackStore,
    private val threadStore: ThreadInsightIndexContentService = ThreadInsightIndexContentService(urlBuilderProvider = StandardUrlBuilderProvider()),
    private val botAccountService: BotAccountService = InstallationBotAccountService(urlBuilderProvider = StandardUrlBuilderProvider()),
    private val completionService: InstrumentedCompletionService,
    private val eventEnqueueService: EventEnqueueService,
    private val inferenceTemplateService: MLInferenceTemplateService,
    private val embeddingService: IEmbeddingService,
) : EventHandler {
    /**
     * Analyzes feedback within a specified date range and optionally for a specific organization.
     *
     * This function retrieves feedback messages from the database within the given date range
     * and creates a new conversation analysis. Each feedback message is then enqueued as an event
     * for further processing.
     *
     * @param fromDate the start date for the feedback analysis.
     * @param toDate the end date for the feedback analysis. Must be after `fromDate`.
     * @param orgId optional organization ID to filter the feedback; if null, feedback for all organizations is considered.
     * @return a new instance of [ConversationAnalysis] representing the analysis of the feedback.
     * @throws IllegalArgumentException if `toDate` is not after `fromDate`.
     * @throws IllegalStateException if no feedback is found for the specified criteria.
     */
    suspend fun analyzeFeedback(
        fromDate: Instant,
        toDate: Instant,
        orgId: OrgId? = null,
        name: String? = null,
        feedbackType: FeedbackType = FeedbackType.Negative,
    ): ConversationAnalysis = withLoggingContextAsync(
        "orgId" to (orgId ?: "crossOrg"),
        "name" to name,
    ) {
        LOGGER.infoAsync(
            "fromDate" to fromDate,
            "toDate" to toDate,
            "feedbackType" to feedbackType,
        ) { "Analyzing feedback" }
        require(fromDate <= toDate) { "fromDate must be before toDate" }

        val feedback = suspendedTransaction {
            messageFeedbackStore.findFeedback(trx = this, orgId = orgId, fromDate = fromDate, toDate = toDate, feedbackType = feedbackType)
                .filter {
                    when (feedbackType) {
                        FeedbackType.Negative -> it.feedbackDescription != null
                        else -> true
                    }
                }
                .map { it.asDataModel() }
        }

        if (feedback.isEmpty()) {
            throw IllegalStateException("No feedback with description found for orgId=$orgId from $fromDate to $toDate")
        }

        val analysis = conversationAnalysisStore.idempotentCreateAnalysis(
            analysisId = ConversationAnalysisId.random(),
            orgId = orgId,
            fromDate = fromDate,
            toDate = toDate,
            totalConversationsCount = feedback.size,
            name = name,
            analysisType = feedbackType.toConversationAnalysisType(),
        )
        LOGGER.infoAsync(
            "Number of feedback to analyze" to feedback.size,
            "analysisId" to analysis.id,
        ) { "Analyzing feedback" }

        feedback.forEach {
            val message = FeedbackAnalysisRequest(
                id = ConversationAnalysisResultHumanFeedbackId.random(),
                feedbackId = it.id,
                analysisId = analysis.id,
                feedbackCreatedAt = it.createdAt,
                orgId = orgId,
                orgMemberId = it.orgMemberId,
                messageId = it.messageId,
                threadId = it.threadId,
                feedbackDescription = it.feedbackDescription,
                feedbackType = feedbackType,
            )
            eventEnqueueService.enqueueEvent(body = message.encode())
        }
        LOGGER.infoAsync("analysis" to analysis) { "All analysis requests sent" }
        return@withLoggingContextAsync analysis
    }

    /**
     * Handle an event which is a json string representation of a [FeedbackAnalysisRequest].
     *
     * This function will:
     * 1. Create a new [HumanFeedbackConversationAnalysisResult] from the request.
     * 2. Calculate the programmatic facets of the conversation.
     * 3. Create a prompt for the LLM to generate facets.
     * 4. Query the LLM to generate facets.
     * 5. Update the [HumanFeedbackConversationAnalysisResult] with the generated facets.
     * 6. Increment the processed count of the associated [ConversationAnalysis].
     *
     * @param event the json string representation of a [FeedbackAnalysisRequest].
     * @return true if the event was successfully handled.
     */
    override suspend fun handle(event: String): Boolean {
        LOGGER.infoAsync("event" to event) { "Handling event" }
        val request = event.decode<FeedbackAnalysisRequest>()
        return withLoggingContextAsync(
            "feedbackId" to request.feedbackId,
            "analysisId" to request.analysisId,
            "resultId" to request.id,
            "threadId" to request.threadId,
            "feedbackType" to request.feedbackType,
            "org" to request.orgId,
        ) {
            val result = conversationAnalysisResultHumanFeedbackStore.idempotentCreate(
                state = ExecutionState.Running,
                resultId = request.id,
                analysisId = request.analysisId,
                feedbackId = request.feedbackId,
                threadId = request.threadId,
                feedbackTypeValue = request.feedbackType,
            )
            runSuspendCatching {
                val (programmaticFacets, llmFacets) = extractFacets(request)
                val (conversationSummaryEmbedding, userFeedbackSummaryEmbedding, querySummaryEmbedding) = extractEmbeddings(
                    llmFacets,
                    request.feedbackType,
                )
                conversationAnalysisResultHumanFeedbackStore.update(
                    resultId = result.id,
                    executionState = ExecutionState.Completed,
                    averageTimeToAnswer = programmaticFacets.averageTimeToAnswer,
                    numberOfTurns = programmaticFacets.numberOfTurns,
                    conversationDuration = programmaticFacets.conversationDuration,
                    humanFeedbackLocationTurn = programmaticFacets.humanFeedbackLocationTurn,
                    conversationSummary = llmFacets.conversationSummary,
                    userFeedbackSummary = llmFacets.userFeedbackSummary,
                    querySummary = llmFacets.querySummary,
                    language = llmFacets.language,
                    conversationSummaryEmbedding = conversationSummaryEmbedding,
                    userFeedbackSummaryEmbedding = userFeedbackSummaryEmbedding,
                    querySummaryEmbedding = querySummaryEmbedding,
                )
                LOGGER.infoAsync { "Single feedback analysis completed" }
            }.onFailure { e ->
                conversationAnalysisResultHumanFeedbackStore.update(
                    resultId = result.id,
                    executionState = ExecutionState.Failed,
                )
                LOGGER.errorAsync(e) { "Failed to handle event" }
            }
            return@withLoggingContextAsync true
        }
    }

    private suspend fun extractEmbeddings(llmFacets: LLMFacets, feedbackType: FeedbackType): Triple<ByteArray?, ByteArray?, ByteArray> {
        // Get embeddings for both summaries
        // TODO: this is not used now, so no reason to produce the embeddings
        // val conversationSummaryEmbedding = createEmbeddingForString(llmFacets.conversationSummary)
        val userFeedbackSummaryEmbedding =
            if (feedbackType == FeedbackType.Negative) createEmbeddingForString(llmFacets.userFeedbackSummary) else null
        val querySummaryEmbedding = createEmbeddingForString(llmFacets.querySummary)

        return Triple(null, userFeedbackSummaryEmbedding, querySummaryEmbedding)
    }

    private suspend fun createEmbeddingForString(input: String) =
        embeddingService.getDocumentEmbedding(input = input, embeddingModel = EmbeddingModel.E5Mistral)
            .denseVector.toByteArray()

    private suspend fun extractFacets(request: FeedbackAnalysisRequest): Pair<ProgrammaticFacets, LLMFacets> {
        val content = threadStore.getThreadInsightContent(threadId = request.threadId, asMarkdown = true)
            ?: throw IllegalStateException("Failed to get thread content")
        val botIds = botAccountService.getBotAccountMembersIds(orgId = content.thread.orgId)
        val programmaticFacets = calculateProgrammaticFacets(messages = content.messages, botIds = botIds, feedbackAtMessage = request.messageId)
        val llmFacets = extractLLMFacets(request, content)
        LOGGER.debugAsync(
            "llmFacets" to llmFacets,
            "programmaticFacets" to programmaticFacets,
            "feedback" to request.feedbackDescription,
        ) { "Got facets" }

        return Pair(programmaticFacets, llmFacets)
    }

    private suspend fun extractLLMFacets(
        request: FeedbackAnalysisRequest,
        content: ThreadInsightIndexContentModel,
    ): LLMFacets {
        val template = getInferenceTemplate(request.orgId, MLInferenceTemplateKind.HumanFeedbackAnalysis)
        val conversationTranscript = compileConversationTranscript(content, request).trimIndent()

        // language=markdown
        val prompt = """${template.promptTemplate}
            |${template.jsonOutputFormatInstructions}
            |[[USER]]
            |# FEEDBACK TYPE
            |You are analyzing feedback type:${request.feedbackType.name}
            |# CONVERSATION
            |The following is a conversation between "Unblocked", an AI assistant, and a user:
            |$conversationTranscript
""".trimMargin()
        val resultJson = completionService.query(
            prompt = prompt,
            template = template,
        )
        return resultJson.decodeLLMOutput<LLMFacets>()
    }

    private suspend fun getInferenceTemplate(orgId: OrgId?, templateKind: MLInferenceTemplateKind): MLInferenceTemplate {
        val template = when (orgId) {
            null -> inferenceTemplateService.getGlobal(templateKind)
            else -> inferenceTemplateService.orgTemplate(orgId, templateKind)
        }
        return template
    }

    private fun calculateProgrammaticFacets(
        messages: List<InsightIndexContent>,
        botIds: Set<MemberId>,
        feedbackAtMessage: MessageId,
    ): ProgrammaticFacets {
        var totalBotReplyTime = 0L
        var numberOfBotReplies = 0
        var humanFeedbackLocationTurn = 0
        messages.forEachIndexed { index, message ->
            if (message.authorId != null && botIds.contains(message.authorId)) {
                val answerTime = if (index == 0) Duration.ZERO else message.modifiedAt - messages[index - 1].createdAt
                totalBotReplyTime += answerTime.inWholeMilliseconds
                numberOfBotReplies += 1
            }
            if (message.id == feedbackAtMessage.value) {
                humanFeedbackLocationTurn = numberOfBotReplies
            }
        }
        val conversationDuration = messages.last().createdAt - messages.first().createdAt
        return ProgrammaticFacets(
            averageTimeToAnswer = if (numberOfBotReplies == 0) -1 else totalBotReplyTime / numberOfBotReplies,
            numberOfTurns = numberOfBotReplies,
            conversationDuration = conversationDuration.inWholeMilliseconds,
            humanFeedbackLocationTurn = humanFeedbackLocationTurn,
        )
    }

    suspend fun clusterFeedback(
        conversationAnalysisId: ConversationAnalysisId,
        maxClusters: Int,
        numberOfRuns: Int,
    ) = withLoggingContextAsync("conversationAnalysisId" to conversationAnalysisId) {
        val analysis = conversationAnalysisStore.get(id = conversationAnalysisId)
        val allResults = conversationAnalysisResultHumanFeedbackStore.findByAnalysisId(analysisId = conversationAnalysisId)
        val feedbackDataPoints = allResults
            .filter { it.userFeedbackSummaryEmbedding != null }
            .map {
                val userFeedbackSummaryEmbedding = it.userFeedbackSummaryEmbedding ?: ByteArray(0)
                DataPoint(points = userFeedbackSummaryEmbedding.toMLVector().toDoubleArray(), conversationAnalysisResultHumanFeedback = it)
            }

        val (feedbackClusters, feedbackClusterMembership) =
            if (analysis.analysis.analysisType == ConversationAnalysisType.FEEDBACK_NEGATIVE) {
                clusterDataPoints(
                    dataPoints = feedbackDataPoints,
                    maxClusters = maxClusters,
                    numberOfRuns = numberOfRuns,
                    analysis = analysis,
                    clusteringType = ClusteringType.Feedback,
                )
            } else {
                Pair(emptyList(), emptyMap())
            }

        val queryDataPoints = allResults
            .filter { it.querySummaryEmbedding != null }
            .map {
                val querySummaryEmbedding = it.querySummaryEmbedding ?: ByteArray(0)
                DataPoint(points = querySummaryEmbedding.toMLVector().toDoubleArray(), conversationAnalysisResultHumanFeedback = it)
            }
        val (queryClusters, queryClusterMembership) = clusterDataPoints(
            dataPoints = queryDataPoints,
            maxClusters = maxClusters,
            numberOfRuns = numberOfRuns,
            analysis = analysis,
            clusteringType = ClusteringType.Query,
        )
        val allMembership = (queryClusterMembership.keys + feedbackClusterMembership.keys).toSet().associateWith {
            listOfNotNull(feedbackClusterMembership[it], queryClusterMembership[it])
        }
        // TODO: this should be distributed via ActiveMQ as the list of results can be very large
        allMembership.forEach { (resultId, clusterIds) ->
            conversationAnalysisResultHumanFeedbackStore.update(
                resultId = resultId,
                clusterMembership = clusterIds,
            )
        }

        val clustersToStore = ClusteringResults(feedbackClusters = feedbackClusters, queryClusters = queryClusters)
        conversationAnalysisStore.update(analysisId = conversationAnalysisId, clusters = clustersToStore)
    }

    private suspend fun clusterDataPoints(
        dataPoints: List<DataPoint>,
        maxClusters: Int,
        numberOfRuns: Int,
        analysis: ConversationAnalysisAggregate,
        clusteringType: ClusteringType,
    ): Pair<List<ClusterInfo>, Map<ConversationAnalysisResultHumanFeedbackId, String>> {
        val centroidClusters: List<CentroidCluster<DataPoint>> = calculateKMeansClusters(dataPoints, maxClusters, numberOfRuns)

        val summaries: List<Pair<CentroidCluster<DataPoint>, ClusterSummary>> =
            summarizeClustersParallel(centroidClusters = centroidClusters, analysis = analysis, clusteringType = clusteringType)

        val clusters: Map<ClusterInfo, CentroidCluster<DataPoint>> = summaries.mapIndexed { index, clusterWithSummary ->
            val cluster = clusterWithSummary.first
            val summary = clusterWithSummary.second
            val id = "${clusteringType.idPrefix}$index"
            ClusterInfo(
                id = id,
                summary = summary.summary,
                label = summary.label,
                clusterMembersCount = cluster.points.size,
            ) to cluster
        }.toMap()

        val clusterMembership = clusters.flatMap { (clusterInfo, cluster) ->
            cluster.points.map { point ->
                point.conversationAnalysisResultHumanFeedback.id to clusterInfo.id
            }
        }.toMap()
        return Pair(clusters.keys.toList(), clusterMembership)
    }

    suspend fun deleteAnalysis(analysisId: ConversationAnalysisId) {
        conversationAnalysisStore.delete(analysisId = analysisId)
    }

    private suspend fun summarizeClustersParallel(
        centroidClusters: List<CentroidCluster<DataPoint>>,
        maxConcurrent: Int = INFERENCE_PARALLELISM,
        analysis: ConversationAnalysisAggregate,
        clusteringType: ClusteringType,
    ): List<Pair<CentroidCluster<DataPoint>, ClusterSummary>> = coroutineScope {
        centroidClusters
            .chunked(maxConcurrent)
            .map { chunk ->
                chunk.map { cluster ->
                    async(Dispatchers.IO) {
                        summarizeCluster(cluster, analysis, clusteringType)
                    }
                }.awaitAll()
            }.flatten()
    }

    private suspend fun summarizeCluster(
        cluster: CentroidCluster<DataPoint>,
        analysis: ConversationAnalysisAggregate,
        clusteringType: ClusteringType,
    ): Pair<CentroidCluster<DataPoint>, ClusterSummary> {
        val template = getInferenceTemplate(analysis.org?.id, MLInferenceTemplateKind.ClusterLabeling)
        val summaries = sampleSummaries(cluster).joinToString("\n") { point ->
            when (clusteringType) {
                ClusteringType.Query -> "- ${point.conversationAnalysisResultHumanFeedback.querySummary ?: ""}"
                ClusteringType.Feedback -> "- ${point.conversationAnalysisResultHumanFeedback.userFeedbackSummary ?: ""}"
            }
        }

        // language=markdown
        val prompt = """${template.promptTemplate}

# Cluster type
You are summarizing cluster type: "${clusteringType.name}". Make sure you are following instructions for this type specifically.

${template.jsonOutputFormatInstructions}
[[USER]]
# Summaries

$summaries"""
        val summary = completionService.query(
            prompt = prompt,
            template = template,
        )
        val summaryData = summary.decodeLLMOutput<ClusterSummary>()
        return Pair(cluster, summaryData)
    }

    /**
     * Randomly samples summaries from the cluster for up to [limit]
     */
    private fun sampleSummaries(cluster: CentroidCluster<DataPoint>, limit: Int = SUMMARY_LIMIT_FOR_CLUSTER_LABELING): List<DataPoint> {
        return cluster.points.shuffled().take(minOf(limit, cluster.points.size))
    }

    suspend fun createExecutiveSummary(analysisId: ConversationAnalysisId, additionalInstructions: String? = null) {
        val analysis = conversationAnalysisStore.get(id = analysisId)
        val (clusters, analysisTypeDescription) = when (analysis.analysis.analysisType) {
            ConversationAnalysisType.FEEDBACK_NEGATIVE -> Pair(
                analysis.analysis.clusters?.feedbackClusters,
                "clusters of user negative user feedback",
            )

            ConversationAnalysisType.FEEDBACK_POSITIVE -> Pair(
                analysis.analysis.clusters?.queryClusters,
                "clusters of user questions that received positive user feedback",
            )

            else -> throw IllegalArgumentException("Unsupported analysis type: ${analysis.analysis.analysisType}")
        }
        require(!clusters.isNullOrEmpty()) { "Analysis $analysisId has no clusters" }

        val clustersPrompt = clusters.joinToString("\n") { cluster ->
            """### Cluster `${cluster.label}`:
                |Members: ${cluster.clusterMembersCount}
                |Summary: ${cluster.summary}
            """.trimMargin()
        }

        val additionalInstructionsPrompt = when {
            additionalInstructions.isNullOrBlank() -> {
                ""
            }

            else -> {
                """## Additional Instructions from the Admin:
The user provided the following additional instructions for you. Prioritize these:
```
$additionalInstructions
```"""
            }
        }
        // language=markdown
        val prompt = """[[SYSTEM]]
You are a seasoned **Data Analyst and Product Strategist** specializing in user feedback analysis for a developer tool called "Unblocked."

You are given $analysisTypeDescription from the Unblocked analytics system.
Each cluster is defined by its **name**, **membership count**, and a brief **summary** highlighting user concerns within the cluster.

Your goal is to produce an **executive summary** based on these clusters, presented as a single **Markdown**-formatted text.
Your report will be presented to the executive team of Unblocked, and it should be consicise and claer.
In this summary, you must:
1. **Identify Major Themes**: Draw from each cluster’s summary to highlight its primary user concerns and overall focus.
2. **Incorporate Membership Details**: Consider how many feedback items (members) are in each cluster. If a cluster is notably large or small compared to others, call it out—large clusters often indicate a widespread issue, while significantly smaller clusters may be outliers and merit less focus.
3. **Correlate and Compare**: If you observe similarities or overlapping patterns across multiple clusters (e.g., repeated concerns about configuration, documentation, or environment setup), point them out. This helps convey how different clusters might share underlying problems.
4. **Maintain an Executive Tone**: Provide succinct, high-level insights. Avoid diving into excessive technical detail.
5. **Outlier Handling**: If a cluster has very few members compared to the rest, mention it briefly as an outlier. Do not spend much time on it unless it reveals a new or critical insight.
6. **Markdown Formatting**: Use headings, bullet points, or other Markdown elements to make the summary clear, readable, and visually appealing.
7. **Summary section**: The output should include a final executive summary explaining key problems that users are reporting.
8. **Avoid recomendations**: do not provide recomendations how to fix the underlying isues, focus on identifying the most pressing issues.

The **output** should be a **single, coherent Markdown text** that can be displayed on a webpage, requiring no further formatting or explanation.

$additionalInstructionsPrompt

[[USER]]
## Clusters
$clustersPrompt

Now provide the report. Include only the report text nothing else.
"""
        val summary = completionService.query(
            prompt = prompt,
            engine = MLInferenceEngine.GPTo3Mini,
            temperature = 0.2f,
        )
        val newClusteringResults = analysis.analysis.clusters?.copy(
            executiveSummary = ExecutiveSummary(summary),
        )
        conversationAnalysisStore.update(analysisId = analysisId, clusters = newClusteringResults)
    }
}

private enum class ClusteringType(val idPrefix: String) {
    Query("Q"),
    Feedback("F"),
}

@Serializable
private data class ClusterSummary(
    val summary: String,
    val label: String,
)

private fun compileConversationTranscript(content: ThreadInsightIndexContentModel, request: FeedbackAnalysisRequest): String {
    val description = request.feedbackDescription ?: "<no description>"
    val feedbackLabel = when (request.feedbackType) {
        FeedbackType.Positive -> "Up-Vote"
        FeedbackType.Negative -> "Down-Vote"
        else -> "Feedback"
    }
    val conversation = content.messages.sortedBy { it.createdAt }
        .fold(StringBuilder()) { acc, message ->
            acc.append("<message>\n")
            acc.append("${message.createdAt} **${message.authorDisplayName}**: \n\t${message.content.joinToString("\n")}")
            if (message.id == request.messageId.value) {
                acc.append("\n\t<userFeedback>\n\t\t**User $feedbackLabel Feedback**: $description\n\t</userFeedback>")
            }
            acc.append("\n</message>\n")
        }.toString()
    return conversation
}

private fun MLVector.toByteArray(): ByteArray = ByteBuffer
    .allocate(size * Double.SIZE_BYTES)
    .order(ByteOrder.LITTLE_ENDIAN)
    .apply { forEach { putDouble(it) } }
    .array()

private fun ByteArray.toMLVector(): MLVector = ByteBuffer
    .wrap(this)
    .order(ByteOrder.LITTLE_ENDIAN)
    .let { buffer ->
        List(size / Double.SIZE_BYTES) { buffer.getDouble() }
    }

private data class ProgrammaticFacets(
    val averageTimeToAnswer: Long,
    val numberOfTurns: Int,
    val conversationDuration: Long,
    val humanFeedbackLocationTurn: Int,
)

@Serializable
private data class LLMFacets(
    val conversationSummary: String,
    val querySummaryDraft: String,
    val userFeedbackSummaryDraft: String,
    val generalizationVocabulary: Map<String, String>,
    val querySummary: String,
    val userFeedbackSummary: String,
    val language: String,
)

private suspend inline fun <reified T> String.decodeLLMOutput(): T {
    return try {
        this.decode<T>()
    } catch (e: SerializationException) {
        LOGGER.errorAsync(e, "resultJson" to this) { "Failed to decode LLMFacets from JSON, LLM Produced invalid JSON" }
        throw e
    }
}
