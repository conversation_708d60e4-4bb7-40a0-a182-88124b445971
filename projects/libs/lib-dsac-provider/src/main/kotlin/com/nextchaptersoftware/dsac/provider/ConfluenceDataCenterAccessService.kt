package com.nextchaptersoftware.dsac.provider

import com.nextchaptersoftware.db.models.ConfluenceSpaceId
import com.nextchaptersoftware.db.models.ConfluenceSpacePermissionEntityType
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.stores.ConfluenceGroupStore
import com.nextchaptersoftware.db.stores.ConfluenceSpacePermissionStore
import com.nextchaptersoftware.db.stores.Stores

class ConfluenceDataCenterAccessService(
    private val confluenceGroupStore: ConfluenceGroupStore = Stores.confluenceGroupStore,
    private val confluenceSpacePermissionStore: ConfluenceSpacePermissionStore = Stores.confluenceSpacePermissionStore,
) {
    // TODO Depending on the server version, groups can have child groups where permissions are inherited
    // https://confluence.atlassian.com/doc/managing-nested-groups-229838455.html
    suspend fun allowedSpaces(
        installationId: InstallationId,
        identities: List<Identity>,
        spaceIds: List<ConfluenceSpaceId>,
    ): Set<ConfluenceSpaceId> {
        val groupIds = identities
            .flatMap { confluenceGroupStore.listGroupsForUser(installationId = installationId, userId = it.externalId) }
            .map { it.groupId }
            .toSet()

        val userIds = identities
            .map { it.externalId }
            .toSet()

        val permissions = confluenceSpacePermissionStore.list(spaceIds = spaceIds)

        return permissions.filter {
            when (it.entityType) {
                ConfluenceSpacePermissionEntityType.Group -> it.entityId in groupIds
                ConfluenceSpacePermissionEntityType.User -> it.entityId in userIds
                ConfluenceSpacePermissionEntityType.Anonymous -> true
            }
        }.map {
            it.confluenceSpaceId
        }.toSet()
    }
}
