package com.nextchaptersoftware.linear.auth.oauth

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.auth.oauth.OAuthStateProvider
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.utils.Base64.base64Decode
import com.nextchaptersoftware.utils.Base64.base64Encode
import io.ktor.http.Url
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable

@Serializable
data class LinearAuthState(
    @Contextual val orgId: OrgId,
    @Contextual val personId: PersonId,
    @Contextual val redirectUrl: Url?,
) : OAuthStateProvider {
    override fun encodeAuthState(): String {
        return encodeAuthState(this)
    }

    companion object {
        fun encodeAuthState(confluenceAuthState: LinearAuthState): String {
            return confluenceAuthState.encode().base64Encode()
        }

        fun decodeAuthState(rawConfluenceAuthState: String): LinearAuthState {
            return rawConfluenceAuthState.base64Decode().decode()
        }
    }
}
