package com.nextchaptersoftware.coda.ingestion.handlers

import com.nextchaptersoftware.coda.api.CodaApiProvider
import com.nextchaptersoftware.coda.api.CodaAuthProvider
import com.nextchaptersoftware.coda.events.enqueue.CodaEventEnqueueService
import com.nextchaptersoftware.coda.events.payloads.CodaEvent
import com.nextchaptersoftware.db.models.CodaInstallation
import com.nextchaptersoftware.db.models.CodaOrganization
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.stores.CodaFolderStore
import com.nextchaptersoftware.db.stores.CodaOrganizationStore
import com.nextchaptersoftware.db.stores.CodaResourceStore
import com.nextchaptersoftware.db.stores.CodaWorkspaceStore
import com.nextchaptersoftware.db.stores.EmbeddingDeleteStore
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.log.kotlin.debugAsync
import io.ktor.client.plugins.ClientRequestException
import io.ktor.http.HttpStatusCode
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

class SyncWorkspaceEventHandler(
    private val apiProvider: CodaApiProvider,
    private val authProvider: CodaAuthProvider,
    private val codaEventEnqueueService: CodaEventEnqueueService,
    private val installationStore: InstallationStore = Stores.installationStore,
    private val codaOrganizationStore: CodaOrganizationStore = Stores.codaOrganizationStore,
    private val codaWorkspaceStore: CodaWorkspaceStore = Stores.codaWorkspaceStore,
    private val codaFolderStore: CodaFolderStore = Stores.codaFolderStore,
    private val codaResourceStore: CodaResourceStore = Stores.codaResourceStore,
    private val embeddingDeleteStore: EmbeddingDeleteStore = Stores.embeddingDeleteStore,
) : TypedEventHandler<CodaEvent.SyncWorkspaceEvent> {
    override suspend fun handle(event: CodaEvent.SyncWorkspaceEvent): Boolean {
        codaOrganizationStore.findById(id = event.codaOrganizationId)?.let { codaOrganization ->
            val installation = installationStore.findById(installationId = codaOrganization.installationId) ?: run {
                LOGGER.debugAsync("codaOrganizationID" to event.codaOrganizationId) { "No installation found" }
                return true
            }

            val accessToken = authProvider.getAccessToken(installation = installation as CodaInstallation)

            try {
                val workspace = apiProvider.workspacesApi.getWorkspace(
                    organizationId = codaOrganization.organizationId,
                    workspaceId = event.workspaceId,
                    accessToken = accessToken,
                )

                val codaWorkspace = codaWorkspaceStore.upsert(
                    codaOrganizationId = codaOrganization.id,
                    workspaceId = workspace.id,
                    name = workspace.name,
                )

                codaEventEnqueueService.enqueueSyncWorkspaceMembersEvent(
                    installationId = installation.id,
                    workspaceId = codaWorkspace.id,
                )
            } catch (it: ClientRequestException) {
                when (it.response.status) {
                    HttpStatusCode.NotFound -> {
                        deleteWorkspace(
                            installation = installation,
                            codaOrganization = codaOrganization,
                            workspaceId = event.workspaceId,
                        )
                    }

                    else -> throw it
                }
            }
        }

        return true
    }

    private suspend fun deleteWorkspace(
        installation: Installation,
        codaOrganization: CodaOrganization,
        workspaceId: String,
    ) {
        codaWorkspaceStore.findByWorkspaceId(
            codaOrganizationId = codaOrganization.id,
            workspaceId = workspaceId,
        )?.let { codaWorkspace ->
            val folders = codaFolderStore.list(codaWorkspaceId = codaWorkspace.id)

            codaResourceStore
                .list(installationId = installation.id)
                .filter { resource -> folders.any { it.folderId == resource.codaId } }
                .forEach {
                    embeddingDeleteStore.markGroupForDeletion(
                        namespaceId = installation.orgId,
                        installationId = installation.id,
                        groupId = it.id.value,
                    )

                    codaResourceStore.delete(installationId = installation.id, ids = listOf(it.id))
                }

            codaWorkspaceStore.delete(codaOrganizationId = codaOrganization.id, id = codaWorkspace.id)
        }
    }
}
