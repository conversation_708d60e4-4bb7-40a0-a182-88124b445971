package com.nextchaptersoftware.search.semantic.services.mcp

import com.nextchaptersoftware.db.models.MLInference
import com.nextchaptersoftware.db.models.MLInferenceCategory
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.stores.MLInferenceStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.dsac.DataSourceAccessControlManager
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.links.LinkProcessorFactory
import com.nextchaptersoftware.mcp.McpToolParameters
import com.nextchaptersoftware.scm.services.CommitService

class McpToolPRDetails(
    private val inferenceStore: MLInferenceStore = Stores.mlInferenceStore,
    private val commitService: CommitService,
    private val linkProcessorFactory: LinkProcessorFactory,
    private val dataSourceAccessControlManager: DataSourceAccessControlManager = DataSourceAccessControlManager(),
) {
    suspend fun run(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        parameters: McpToolParameters.PRDetails,
    ): MLInference? {
        return when (val prNumber = parameters.prNumber) {
            null -> when (val url = parameters.prUrl) {
                null -> null

                else -> {
                    val dsacContext = dataSourceAccessControlManager.getDsacContext(orgId = orgId, orgMemberId = orgMemberId)
                    linkProcessorFactory
                        .createProcessor(
                            orgId = orgId,
                            orgMemberId = orgMemberId,
                            dsacContext = dsacContext,
                        )
                        .processText(inputText = url.asString)
                        .let { linkedDocuments ->
                            """
                            |### PR Details for ${url.asString}
                            |${linkedDocuments.joinToString("\n") {
                                """
                                |* ${it.document.title} *
                                |${it.document.content}
                                |
                                """.trimMargin()
                                }
                            }
                            """.trimMargin()
                        }
                }
            }

            else -> commitService.getPullRequestDetails(
                orgId = orgId,
                orgMemberId = orgMemberId,
                repoId = RepoId(parameters.repoId),
                pullRequestNumber = prNumber,
                overrideRepoAccess = null,
            )
        }?.let { prDetails ->
            inferenceStore.createExample(
                orgId = orgId,
                botMessageId = null,
                questionerOrgMemberId = orgMemberId,
                runtimeConfigurationId = null,
                runtimeConfigType = null,
                category = MLInferenceCategory.Mcp,
                templateId = null,
                query = parameters.query,
                documentQuery = parameters.query,
                response = prDetails,
                rawResponse = prDetails,
                isSuggestion = false,
                runDuration = null,
                executionTrace = null,
                validationInferenceId = null,
                validationDistance = null,
                prompt = null,
                documents = null,
                references = null,
                dataSourcePreset = null,
                productAgent = ProductAgentType.Mcp,
            ).asDataModel()
        }
    }
}
