package com.nextchaptersoftware.search.semantic.services.chain.steps

import com.nextchaptersoftware.log.sensitive.errorSensitiveAsync
import com.nextchaptersoftware.ml.doc.rerank.services.DocumentRerankService
import com.nextchaptersoftware.ml.query.context.DocumentContext
import com.nextchaptersoftware.ml.query.context.QueryContext
import com.nextchaptersoftware.search.semantic.services.chain.ChainExecutionContext
import com.nextchaptersoftware.search.semantic.services.chain.InstrumentedChainStep
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class DocumentRerankStep(
    private val documentRerankService: DocumentRerankService,
) : InstrumentedChainStep<QueryContext, DocumentContext, DocumentContext> {
    override suspend fun execute(context: ChainExecutionContext<QueryContext>, input: DocumentContext): DocumentContext {
        if (input.documents.isEmpty()) {
            return input
        }

        @Suppress("TooGenericExceptionCaught") // Ignore: We need to catch TimeoutCancellationException
        return when (context.inputContext.queryTemplate.useCERR) {
            true -> try {
                documentRerankService.rerankDocuments(
                    orgId = context.inputContext.org.id,
                    input,
                    template = context.inputContext.queryTemplate,
                )
            } catch (e: Exception) {
                LOGGER.errorSensitiveAsync(
                    t = e,
                    sensitiveFields = mapOf(
                        "query" to input.documentQueryContext.documentQueryText,
                    ),
                ) { "Failed to rerank documents for inference" }
                input
            }

            false -> input
        }
    }
}
