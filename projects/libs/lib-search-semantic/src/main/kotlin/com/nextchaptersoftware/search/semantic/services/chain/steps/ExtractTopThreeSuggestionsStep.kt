package com.nextchaptersoftware.search.semantic.services.chain.steps

import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.sensitive.warnSensitiveAsync
import com.nextchaptersoftware.ml.query.context.DocumentContext
import com.nextchaptersoftware.ml.query.context.DocumentQueryContext
import com.nextchaptersoftware.ml.query.context.QueryContext
import com.nextchaptersoftware.search.semantic.services.chain.ChainExecutionContext
import com.nextchaptersoftware.search.semantic.services.chain.InstrumentedChainStep
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class ExtractTopThreeSuggestionsStep : InstrumentedChainStep<QueryContext, Map<DocumentQueryContext, DocumentContext>, List<String>> {
    companion object {
        const val SCORE_SAMPLE_SIZE = 10
        const val TOP_THREE = 3
        const val MIN_TOP_SCORE = 0.9f
    }

    override suspend fun execute(
        context: ChainExecutionContext<QueryContext>,
        input: Map<DocumentQueryContext, DocumentContext>,
    ): List<String> {
        if (input.keys.isEmpty()) {
            LOGGER.warnSensitiveAsync(
                sensitiveFields = mapOf(
                    "orgId" to context.inputContext.org.id,
                    "originalQuery" to context.inputContext.query,
                ),
            ) {
                "No suggestions generated for query"
            }
            return emptyList()
        }

        // Take the top three suggestions based on the highest overall document rank score from the top 10 in each result set
        val suggestionsWithScores = input.map { (key, value) ->
            val sample = value.documents.mapNotNull { it.score }.sortedByDescending { it }.take(SCORE_SAMPLE_SIZE)
            val sumScore = sample.sum()
            val topScore = sample.firstOrNull() ?: 0.0f
            key to Pair(sumScore, topScore)
        }

        val topThreeSuggestionsWithScores = suggestionsWithScores
            .filter { it.second.second >= MIN_TOP_SCORE }
            .sortedByDescending { it.second.first }
            .take(TOP_THREE)

        LOGGER.debugAsync(
            "orgId" to context.inputContext.org.id,
            "topScores" to suggestionsWithScores.map { it.second.second },
            "topScoresSum" to suggestionsWithScores.map { it.second.first },
        ) {
            "Suggestions generated"
        }

        val suggestedQuestions = topThreeSuggestionsWithScores.map { it.first.documentQueryText }

        if (suggestedQuestions.isEmpty()) {
            LOGGER.warnSensitiveAsync(
                sensitiveFields = mapOf(
                    "orgId" to context.inputContext.org.id,
                    "originalQuery" to context.inputContext.query,
                ),
            ) {
                "Suggestions were generated but none ranked high enough for inclusion"
            }
        }

        return suggestedQuestions
    }
}
