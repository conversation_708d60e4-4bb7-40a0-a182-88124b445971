package com.nextchaptersoftware.plan.capabilities

import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgBilling
import com.nextchaptersoftware.db.ModelBuilders.makePlan
import com.nextchaptersoftware.db.ModelBuilders.makePlanCapability
import com.nextchaptersoftware.db.models.PlanCapabilityType
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class PlanCapabilitiesServiceTest : DatabaseTestsBase() {
    private val service = PlanCapabilitiesServiceProvider(config = GlobalConfig.INSTANCE.billing).get()

    @Test
    fun resolveCapabilities() = suspendingDatabaseTest {
        val planA = makePlan()
        val planB = makePlan(basePlan = planA)
        val planC = makePlan(basePlan = planB)
        val planD = makePlan(basePlan = planC)

        makePlanCapability(plan = planB, capability = PlanCapabilityType.Confluence, enabled = true).asDataModel()
        makePlanCapability(plan = planB, capability = PlanCapabilityType.StackOverflowForTeams, enabled = true).asDataModel()
        makePlanCapability(plan = planC, capability = PlanCapabilityType.StackOverflowForTeams, enabled = false).asDataModel()
        makePlanCapability(plan = planC, capability = PlanCapabilityType.Jira, enabled = false).asDataModel()

        assertThat(service.resolveCapabilities(planId = planA.idValue))
            .isEmpty()

        assertThat(service.resolveCapabilities(planId = planB.idValue))
            .isEqualTo(
                mapOf(
                    PlanCapabilityType.Confluence to true,
                    PlanCapabilityType.StackOverflowForTeams to true,
                ),
            )

        val planCCapabilities = service.resolveCapabilities(planId = planC.idValue).also {
            assertThat(it).isEqualTo(
                mapOf(
                    PlanCapabilityType.Confluence to true,
                    PlanCapabilityType.StackOverflowForTeams to false,
                    PlanCapabilityType.Jira to false,
                ),
            )
        }

        assertThat(service.resolveCapabilities(planId = planD.idValue))
            .isEqualTo(planCCapabilities)
    }

    @Test
    fun hasCapability() = suspendingDatabaseTest {
        val org = makeOrg()
        val planA = makePlan()
        val planB = makePlan(basePlan = planA)
        val planC = makePlan(basePlan = planB)

        makePlanCapability(plan = planB, capability = PlanCapabilityType.Confluence, enabled = true).asDataModel()
        makePlanCapability(plan = planB, capability = PlanCapabilityType.StackOverflowForTeams, enabled = true).asDataModel()
        makePlanCapability(plan = planC, capability = PlanCapabilityType.StackOverflowForTeams, enabled = false).asDataModel()
        makePlanCapability(plan = planC, capability = PlanCapabilityType.Jira, enabled = true).asDataModel()

        makeOrgBilling(org = org, plan = planC)

        assertThat(service.hasCapability(orgId = org.idValue, PlanCapabilityType.StackOverflowForTeams))
            .isFalse()

        assertThat(service.hasCapability(orgId = org.idValue, PlanCapabilityType.Confluence))
            .isTrue()

        assertThat(service.hasCapability(orgId = org.idValue, PlanCapabilityType.Jira))
            .isTrue()

        assertThat(service.hasCapability(orgId = org.idValue, PlanCapabilityType.Slack))
            .isFalse()
    }
}
