package com.nextchaptersoftware.rpc.calls

import com.nextchaptersoftware.api.models.GoogleDriveConfiguration
import com.nextchaptersoftware.api.models.GoogleDriveWorkspace
import com.nextchaptersoftware.api.models.SearchGoogleDriveFilesResponse
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable

/**
 * @see com.nextchaptersoftware.proxy.provider.rpc.handlers.GoogleDriveHandler
 */
interface GoogleDriveCalls {

    suspend fun googleDriveSearchFiles(
        params: GoogleDriveSearchFilesParams,
    ): SearchGoogleDriveFilesResponse

    @Serializable
    data class GoogleDriveSearchFilesParams(
        @Contextual
        val orgMemberId: OrgMemberId,
        @Contextual
        val installationId: InstallationId,
        @Contextual
        val orgId: OrgId,
        val query: String,
    )

    suspend fun googleDriveGetConfiguration(
        params: GoogleDriveGetConfigurationParams,
    ): GoogleDriveConfiguration

    @Serializable
    data class GoogleDriveGetConfigurationParams(
        @Contextual
        val orgId: OrgId,
        @Contextual
        val installationId: InstallationId,
        @Contextual
        val orgMemberId: OrgMemberId,
    )

    suspend fun googleDriveSaveConfiguration(
        params: GoogleDriveSaveConfigurationParams,
    )

    @Serializable
    data class GoogleDriveSaveConfigurationParams(
        @Contextual
        val orgId: OrgId,
        @Contextual
        val installationId: InstallationId,
        @Contextual
        val orgMemberId: OrgMemberId,
        val configuration: GoogleDriveConfiguration,
    )

    suspend fun googleDriveUpsertGoogleWorkspaceDrive(
        params: GoogleDriveUpsertGoogleWorkspaceDriveParams,
    ): GoogleDriveWorkspace

    @Serializable
    data class GoogleDriveUpsertGoogleWorkspaceDriveParams(
        @Contextual
        val orgId: OrgId,
        @Contextual
        val installationId: InstallationId,
        val serviceAccountKey: String,
        val adminEmail: String,
    )
}
