# Default values for service.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
global:
  appSlug: "testunblocked"
  replicated:
    dockerconfig<PERSON><PERSON>: 'dummy'
  unblockedSecretsB64: "dummy"
  unblockedConfigB64: "dummy"
  environment: "onprem"

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  # name: "postgres-access"
